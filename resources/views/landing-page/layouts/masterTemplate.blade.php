<!DOCTYPE html>
{{--<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">--}}
<html lang="en">


    @php
        $platform = session()->get('platform');
    @endphp

    <head>
        <meta charset="UTF-8" />
        <link rel="preload" href="https://rsms.me/inter/font-files/Inter-Medium.woff2?v=4.1" as="font" type="font/woff2" crossorigin="anonymous">
        <link rel="preload" href="https://rsms.me/inter/font-files/Inter-Light.woff2?v=4.1" as="font" type="font/woff2" crossorigin="anonymous">
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta http-equiv="X-UA-Compatible" content="ie=edge" />
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <title>@yield('title')</title>
        <script type="text/javascript" src="hps://cdn.linguise.com/script/placeholder.js" defer></script>
        @yield('seo_meta')
        @include('landing-page.partials.styles')
        @stack('css')
        @livewireStyles
        <link rel="icon" type="image/x-icon" href="{{ asset('cms-assets/img/favicon/favicon1.png') }}" />

        {{-- linguise--}}
        @if(!app()->environment('production'))
            <script async src="https://static.linguise.com/script-js/switcher.bundle.js?d=pk_NitTghUbBaCzJJjUZjaYZWxGUhcQ0KIf"></script>
        @endif
        @if(in_array($platform, ['mobile']))
            <style>
                .linguise_switcher_wrapper {
                    display: none !important;
                }
            </style>
        @endif
        @if (App::environment('production'))
            <script>
                console.log('run gtm');
            </script>
            <!-- Google Tag Manager -->
            <script>
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','GTM-5WBL7ZS3');
            </script>
            <!-- End Google Tag Manager -->
        @endif
    </head>

    <body>
        @include('landing-page.partials.alert')
        @if(!in_array($platform, ['mobile']))
            @include('landing-page.partials.header')
        @endif

        {{-- @include('landing-page.partials.navbar') --}}
        @yield('content')

        @if(!in_array($platform, ['mobile']))
            <div class="mt-10">
            </div>
    {{--        <div class="mt-10">--}}
    {{--            @include('landing-page.partials.homepage.contact')--}}
    {{--        </div>--}}
        {{-- @else
            <div class="mt-10">
            </div> --}}
        @endif

        @if(!in_array($platform, ['mobile']))
            <div class="relative">
                @include('landing-page.partials.navigation-footer')
                @include('landing-page.partials.footer')
            </div>
        @endif

        {{-- @include("partials.floatingTop") --}}
{{--        @if(!in_array($platform, ['mobile']))--}}
{{--            @include('landing-page.partials.live-chat')--}}
{{--        @endif--}}

        @include('landing-page.partials.scripts')
    {{--    @include('landing-page.partials.homepage.component.pre_register')--}}
        @stack('script')

    </body>

</html>
