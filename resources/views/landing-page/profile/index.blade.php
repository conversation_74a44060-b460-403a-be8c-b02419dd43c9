@section('title', 'Bali International Hospital')
@extends('landing-page.layouts.masterTemplate')
@push('css')
    <style>
        .swal2-icon {
            border: none !important;
            margin-top: 1.25em;
            margin-right: auto;
            margin-bottom: 0.5em !important;
        }
        .swal2-title {
            font-size: 20px !important;
            font-weight: 600 !important;
        }
        .swal2-content {
            font-size: 16px !important;
            font-weight: 500 !important;
            color: #667085 !important;
        }
        .swal2-popup {
            width: 400px !important;
            border-radius: 16px !important;
        }
        .swal2-cancel {
            width: 85px !important;
            height: 40px !important;
            border-radius: 8px !important;
            border: 1px solid #0D4D8B !important;
            background: #FFFFFF !important;
            padding: 8px !important;
            color: #0D4D8B !important;
        }
        .swal2-confirm {
            width: 85px !important;
            height: 40px !important;
            border-radius: 8px !important;
            padding: 8px !important;
            background: #0D4D8B !important;
        }
        .swal2-styled:focus {
            box-shadow: none !important;
        }
        .swal2-styled {
            margin: .5125em !important;
        }
        .swal2-close {
            width: 0.32em !important;
            height: 0.32em !important;
            right: 20px !important;
            top: 20px !important;
        }
    </style>
@endpush

@section('content')
<div class="w-full flex mt-24 md:mt-28 border-b-2">
    <div class="mx-4 md:mx-40 flex">
        <div class="flex flex-row gap-4 items-center py-4">
            <a href="/" class="text-primary-blue-60">Home</a>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5"
                      stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <span class="text-neutral-40">My Account</span>
        </div>
    </div>
</div>
<div class="w-full bg-light-gray py-8 content-center">
    <div class="flex items-center justify-center mx-4 md:mx-0">
        <div class="w-full md:w-680 flex-non bg-white drop-shadow-sm px-4 md:px-16 py-8 rounded-3xl">

            <h1 class="font-weight-600 text-xl mb-2">Patient Profile</h1>
            <p class="mt-0 text-neutral-50 text-xs mb-6">Add or manage your own or family member profiles.</p>

            <h1 class="font-weight-400 text-16-24 mb-4">Myself</h1>
            <div class="flex border border-1 rounded-2xl px-2 py-5 shadow-sm hover:shadow-md hover:cursor-pointer mb-6">
                <a class="flex flex-col w-20 px-2 py-2 mr-2 justify-center items-center gap-2 lg:gap-0" href="{{ route('profile.show', $selfPatient->uuid) }}">
                    @php
                        $gcs = new \App\Services\GCS\GoogleCloudService();
                    @endphp
                    @if (!empty($selfPatient->image))
                        <img class="shadow rounded-full h-16 w-16 align-middle border-none" src="{{ $gcs->getStaticUrl($selfPatient->image) }}" alt="profile">
                    @else
                        <div class="shadow rounded-full h-16 w-16 align-middle border-none text-36-44 py-2.5 px-5 bg-primary-blue-60 text-white">{{ strtoupper(substr($selfPatient->first_name, 0, 1)) }}</div>
                    @endif
                    <div class="flex-none overflow-hidden lg:hidden flex">
                        <img src="{{ asset('assets/profile/edit.svg') }}" alt="" class="inline float-end hover:cursor-pointer z-10 edit-profile" data-hs-overlay="#hs-large-modal-{{$selfPatient->uuid}}">
                    </div>
                </a>
                <div class="myself grow" data-link="{{ route('profile.show', $selfPatient->uuid) }}">
                    <p class="text-primary-blue-20 mb-1 flex flex-row gap-1 items-center">
                        @if(@$selfPatient->mr_no)
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                            </svg>
                        @endif
                        <span class="linguise_patient_name">
                            {{ $selfPatient->full_name }}
                        </span>
                    </p>
                    @if(@$selfPatient->mr_no)
                        <div class="flex flex-row gap-1 text-[#667085] text-sm font-light">
                            <span>MR ID: </span>
                            <span>{{ @$selfPatient->mr_no }}</span>
                        </div>
                    @endif
                    @if ($selfPatient->percentage_progress == \App\Enums\Table\Patient\PercentageProgress::COMPLETE_ADDRESS_INFO)
                    <p class="text-neutral-50">{{ date("d F Y", strtotime($selfPatient->dob)) }}</p>
                    @endif
                    <p class="text-neutral-50 mb-1">+{{ count($selfPatient->phoneCountryCodes) > 0 ? $selfPatient->phoneCountryCodes[0]['extension'] : '' }}{{ $selfPatient->contact_no }}</p>

                    @if (!$selfPatient->is_complete_data)
                        <div class="bg-warning-50 inline-block py-1 px-2 rounded-full text-14-20 text-white">
                            {!! file_get_contents('assets/profile/exclamation-circle.svg') !!}
                            Data is incomplete
                        </div>
                    @endif
                </div>
                <div class="flex-none overflow-hidden pr-4 lg:block hidden">
                    <img src="{{ asset('assets/profile/edit.svg') }}" alt="" class="inline float-end hover:cursor-pointer z-10 edit-profile" data-hs-overlay="#hs-large-modal-{{$selfPatient->uuid}}">
                </div>
            </div>

            <div class="flex">
                <div class="flex-1 mt-auto mb-auto">
                    <h1 class="font-weight-400 text-16-24 mb-3">Others</h1>
                </div>
                @if (count($otherPatients) > 0)
                <div class="flex-1 text-right">
                    <button class="h-[3rem] w-fit px-3 rounded-xl text-primary-blue-60 text-16-24 text-center border border-primary-blue-60 font-weight-500" data-hs-overlay="#hs-large-modal">
                        <img src="{{ asset('assets/profile/user-add.svg') }}" alt="" class="inline mr-1">
                        Add New Profile
                    </button>
                </div>
                @endif
            </div>
            <div class="text-center mb-4">
                @if (count($otherPatients) > 0)
                    @foreach($otherPatients as $otherPatient)
                    <div class="flex border border-1 rounded-2xl px-2 py-5 shadow-sm hover:shadow-md hover:cursor-pointer mb-6 mt-4">
                        <a class="flex flex-col w-20 px-2 py-2 mr-2 items-center justify-center gap-2 lg:gap-0" href="{{ route('profile.show', $otherPatient->uuid) }}">
                            @php
                                $gcs = new \App\Services\GCS\GoogleCloudService();
                            @endphp
                            @if (!empty($otherPatient->image))
                                <img class="shadow rounded-full h-16 w-16 align-middle border-none" src="{{ $gcs->getStaticUrl($otherPatient->image) }}" alt="profile">
                            @else
                                <div class="shadow rounded-full h-16 w-16 align-middle border-none text-36-44 py-2.5 px-5 bg-primary-blue-60 text-white">{{ strtoupper(substr($otherPatient->first_name, 0, 1)) }}</div>
                            @endif
                            <div class="overflow-hidden lg:hidden">
                                <img src="{{ asset('assets/profile/trash.svg') }}" alt="" class="inline float-end ml-2 hover:cursor-pointer delete-profile" data-id="{{ $otherPatient->id }}" data-name="{{ $otherPatient->fullname }}">
                                <img src="{{ asset('assets/profile/edit.svg') }}" alt="" class="inline float-end hover:cursor-pointer edit-profile" data-hs-overlay="#hs-large-modal-{{$otherPatient->uuid}}">
                            </div>
                        </a>
                        <div class="flex flex-col gap-1 w-full">
                            <div class="flex flex-row gap-1 justify-between">
                                <div class="other grow text-left" data-link="{{ route('profile.show', $otherPatient->uuid) }}">
                                    <div class="text-primary-blue-20 mb-1 flex flex-row gap-1 items-center">
                                        @if(@$otherPatient->mr_no)
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                                            </svg>
                                        @endif
                                        <div class="linguise_patient_name">
                                            {{ $otherPatient->full_name }}
                                            <span class="text-neutral-50">({{ \App\Enums\Table\Patient\RelationPatient::getLabel((string)$otherPatient->relation_patient) }})</span>
                                        </div>
                                    </div>
                                    @if(@$otherPatient->mr_no)
                                        <div class="flex flex-row gap-1 text-[#667085] text-sm font-light">
                                            <span>MR ID: </span>
                                            <span>{{ @$otherPatient->mr_no }}</span>
                                        </div>
                                    @endif
                                    @if ($otherPatient->percentage_progress == \App\Enums\Table\Patient\PercentageProgress::COMPLETE_ADDRESS_INFO)
                                        <p class="text-neutral-50">{{ date("d F Y", strtotime($otherPatient->dob)) }}</p>
                                    @endif
                                    <p class="text-neutral-50">+{{ count($otherPatient->phoneCountryCodes) > 0 ? $otherPatient->phoneCountryCodes[0]['extension'] : '' }}{{ $otherPatient->contact_no }}</p>
                                </div>
                                <div class="flex-none overflow-hidden pr-4 lg:block hidden">
                                    <img src="{{ asset('assets/profile/trash.svg') }}" alt="" class="inline float-end ml-2 hover:cursor-pointer delete-profile" data-id="{{ $otherPatient->id }}" data-name="{{ $otherPatient->fullname }}">
                                    <img src="{{ asset('assets/profile/edit.svg') }}" alt="" class="inline float-end hover:cursor-pointer edit-profile" data-hs-overlay="#hs-large-modal-{{$otherPatient->uuid}}">
                                </div>
                            </div>
                            @if (!$otherPatient->is_complete_data)
                                <div class="bg-warning-50 inline-block py-1 px-2 rounded-full text-14-20 text-white w-fit">
                                    {!! file_get_contents('assets/profile/exclamation-circle.svg') !!}
                                    Data is incomplete
                                </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                @else
                <div class="bg-white rounded-3xl px-6 py-6 w-[520px] text-center">
                    {!! file_get_contents('assets/profile/user-group.svg') !!}
                    <p class="text-14-20 text-neutral-60">You haven't added any profile for others.</p>
                </div>
                <button class="h-[3rem] w-fit px-3 rounded-xl text-primary-blue-60 text-16-24 text-center border border-primary-blue-60 font-weight-500 new-form" data-hs-overlay="#hs-large-modal">
                    <img src="{{ asset('assets/profile/user-add.svg') }}" alt="" class="inline mr-1">
                    Add New Profiles
                </button>
                @endif

            </div>
            @if (Session::has('error-title') && Session::has('error-description'))
                <div class="bg-red-50 border border-red-200 text-sm text-red-800 rounded-lg p-4 mb-6" role="alert">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="flex-shrink-0 size-4 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m15 9-6 6"/><path d="m9 9 6 6"/></svg>
                        </div>
                        <div class="ms-4 text-wrap">
                            <h3 class="text-sm font-semibold">
                                {{ Session::get('error-title') }}
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                {{ Session::get('error-description') }}
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

{{--<div id="hs-large-modal-{{$otherPatient->uuid}}" class="hs-overlay size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto pointer-events-none" role="dialog" tabindex="-1" aria-labelledby="hs-large-modal-label">--}}
{{--    <div class="hs-overlay-open:mt-7 hs-overlay-open:duration-500 mt-0 opacity-100 ease-out transition-all lg:max-w-5xl lg:w-full m-3 lg:mx-auto">--}}
{{--<div id="hs-large-modal-{{$otherPatient->uuid}}" class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto pointer-events-none" role="dialog" tabindex="-1" aria-labelledby="hs-large-modal-label">--}}
{{--    <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all lg:max-w-5xl lg:w-full m-3 lg:mx-auto">--}}
@foreach($otherPatients as $otherPatient)
    <div id="hs-large-modal-{{$otherPatient->uuid}}" class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto pointer-events-none" role="dialog" tabindex="-1" aria-labelledby="hs-large-modal-label">
        <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all lg:max-w-5xl lg:w-full m-3 lg:mx-auto">
            <div class="flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto">
                <div class="flex justify-between items-center py-3 px-4">
                    <h3 id="hs-large-modal-label" class="font-bold text-gray-800">
                        Edit Profile {{ $otherPatient->first_name . ' ' . $otherPatient->last_name }}
                    </h3>
                    <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none" aria-label="Close" data-hs-overlay="#hs-large-modal-{{$otherPatient->uuid}}">
                        <span class="sr-only">Close</span>
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6 6 18"></path>
                            <path d="m6 6 12 12"></path>
                        </svg>
                    </button>
                </div>
                @php
                    $page = request()->fullUrl();
                @endphp
                <div class="p-4 overflow-y-auto">
                    <livewire:landing-page.profile.new-profile :page="$page" :patient_id="$otherPatient->id" wire:key="patient-{{$otherPatient->uuid}}"/>
                </div>
            </div>
        </div>
    </div>
@endforeach

<div id="hs-large-modal-{{$selfPatient->uuid}}" class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto pointer-events-none" role="dialog" tabindex="-1" aria-labelledby="hs-large-modal-label">
    <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all lg:max-w-5xl lg:w-full m-3 lg:mx-auto">
        <div class="flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto">
            <div class="flex justify-between items-center py-3 px-4">
                <h3 id="hs-large-modal-label" class="font-bold text-gray-800">
                    Edit Profile {{ $selfPatient->first_name . ' ' . $selfPatient->last_name }}
                </h3>
                <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none" aria-label="Close" data-hs-overlay="#hs-large-modal-{{$selfPatient->uuid}}">
                    <span class="sr-only">Close</span>
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>
            </div>
            @php
                $page = request()->fullUrl();
            @endphp
            <div class="p-4 overflow-y-auto">
                <livewire:landing-page.profile.new-profile :page="$page" :patient_id="$selfPatient->id" wire:key="patient-{{$selfPatient->uuid}}"/>
            </div>
        </div>
    </div>
</div>
<div id="hs-large-modal" class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto pointer-events-none" role="dialog" tabindex="-1" aria-labelledby="hs-large-modal-label">
    <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all lg:max-w-5xl lg:w-full m-3 lg:mx-auto">
        <div class="flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto">
            <div class="flex justify-between items-center py-3 px-4">
                <h3 id="hs-large-modal-label" class="font-bold text-gray-800">
                    Add New Profile
                </h3>
                <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none" aria-label="Close" data-hs-overlay="#hs-large-modal">
                    <span class="sr-only">Close</span>
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>
            </div>
            @php
                $page = request()->fullUrl();
            @endphp
            <div class="p-4 overflow-y-auto">
                <livewire:landing-page.profile.new-profile :page="$page" wire:key="patient-{{\Illuminate\Support\Str::uuid()}}"/>
            </div>
        </div>
    </div>
</div>

@endsection
@push('script')
    <script>
        $(document).ready(function (){
            $('.myself').on('click', function (){
                const link = $(this).attr('data-link');
                console.log($(this).attr('data-link'));
                location.replace(link);
            });

            $('.other').on('click', function (){
                const link = $(this).attr('data-link');
                location.replace(link);
            });

            $('.delete-profile').on('click', function (){
                Swal.fire({
                    title: "Remove Patient `"+$(this).attr('data-name')+"`?",
                    text: "You will need to re-add and adjust the data again.",
                    iconHtml: '<img src="{{ asset('assets/profile/modal-trash.svg') }}"',
                    showCancelButton: true,
                    showCloseButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Remove'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            type    : 'GET',
                            url     : "{{ config('app.url') }}" + "/profile/destroy/" + $(this).attr('data-id'),
                            success : (response) => {
                                displayAlert(response.message,'#4fbe87')
                                setTimeout(() => {
                                    location.reload();
                                }, 1000);
                            },
                            error: function(error) {
                                displayAlert(error.responseJSON.message,'#eb311c')
                            }
                        });
                    }
                })
            });
        });


        // function onClickOpenFile(url){
        //     window.open(url, '_blank');
        // }
    </script>
@endpush
