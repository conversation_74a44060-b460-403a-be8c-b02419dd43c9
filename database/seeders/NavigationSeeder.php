<?php

namespace Database\Seeders;

use App\Models\Cms\Navigation;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class NavigationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //

        $this->navigation();
    }

    public function navigation()
    {
        $nav = new Navigation();

        $parents = $nav->ConstantParents();

        foreach ($parents as $item) {
            \App\Models\Cms\Navigation::updateOrCreate([
                'id' => $item['id'],
                'name' => $item['name'],
            ], [
                'id' => $item['id'],
                'name' => $item['name'],
                'url' => $item['url'],
                'icon' => $item['icon'],
                'order' => $item['order'],
                'is_active' => $item['is_active'],
            ]);
        }


        $manag_users = Navigation::where('name', 'Management Users')->first();
        $packages = Navigation::where('name', 'Packages')->first();
        $promos = Navigation::where('name', 'Promos And Banners')->first();
        $articles = Navigation::where('name', 'Management Articles')->first();
        $careers = Navigation::where('name', 'Management Careers')->first();
        $info = Navigation::where('name', 'Informations')->first();
        $bookings = Navigation::where('name', 'Bookings')->first();
        $general_payment = Navigation::where('name', 'General Payments')->first();


        // dd($parents);


        $data = [
            // [
            //     'id' =>  'c4c78e4c-cbb8-4e5a-8e4d-7d383f9561fd',
            //     'name' => 'Dashboard',
            //     'url' => '/cms/dashboard',
            //     'icon' => '',
            //     'order' => 1,
            //     'is_active' => true,
            //     'parent_id' => null
            // ],
            [
                'id' =>  '69b012c6-814c-4ab3-90db-74cc15d8e79c',
                'name' => 'CMS Roles',
                'url' => '/cms/roles',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $manag_users->id
            ],
            [
                'id' =>  '2c96dba2-ae99-4e6c-bc6b-c857a076024c',
                'name' => 'CMS Users',
                'url' => '/cms/users',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $manag_users->id
            ],
            [
                'id' =>  '5e93bf45-8d27-4acd-812e-c572877da602',
                'name' => 'Public Users',
                'url' => '/cms/public-users',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $manag_users->id
            ],
            [
                'id' =>  '5ac425d5-06ce-486f-8dfc-ac74754a77d4',
                'name' => 'Package Types',
                'url' => '/cms/package-types',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $packages->id
            ],
            [
                'id' =>  '9c13bc89-bf6e-4ca9-abd5-a28d6e9e8d26',
                'name' => 'Package Categories',
                'url' => '/cms/package-categories',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $packages->id
            ],
            [
                'id' =>  'c233d886-1921-4893-8df7-99c9b1691fae',
                'name' => 'Packages',
                'url' => '/cms/packages',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $packages->id
            ],
            [
                'id' =>  '2e35a2ce-ada1-4054-bd37-e010aaa6e854',
                'name' => 'Promos & Banner',
                'url' => '/cms/promos',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $promos->id
            ],
            [
                'id' =>  'e05b2d8e-9fb6-43f1-a330-afe319dcf0d6',
                'name' => 'Generate Code Promo',
                'url' => '/cms/promo-codes',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $promos->id
            ],
            [
                'id' =>  '8ed1636d-9b1a-42cc-a952-cf26140f2e59',
                'name' => 'Article Categories',
                'url' => '/cms/article-categories',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $articles->id
            ],
            [
                'id' =>  '34d8e563-d4f1-44e5-b190-2a3cb4ecaff5',
                'name' => 'Articles',
                'url' => '/cms/articles',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $articles->id
            ],
            [
                'id' =>  '90343f8f-5804-430c-b4b4-85a928dee29a',
                'name' => 'Careers',
                'url' => '/cms/careers',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $careers->id
            ],
            [
                'id' =>  '8cea5570-1eab-49cc-bf37-5fdd0c6853f6',
                'name' => 'Announcements',
                'url' => '/cms/announcements',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $info->id
            ],
            [
                'id' => '848a8655-4617-4915-bbd0-9fee2cf2bef3',
                'name' => 'Doctor Profiles',
                'url' => '/cms/doctor-profiles',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => null
            ],
            [
                'id' => '7423e62a-d1c8-3330-851c-7ecbca0743b3',
                'name' => 'Reports',
                'url' => '/cms/reports/users',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => null
            ],
            [
                'id' => 'abbd8e88-2091-4fee-abda-172376c1addf',
                'name' => 'Patients',
                'url' => '/cms/bookings/patients',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => $bookings->id
            ],
            [
                'id' => 'd89ab4ad-848f-45ba-b3ec-193b28766831',
                'name' => 'Appointment',
                'url' => '/cms/bookings/appointments',
                'icon' => '',
                'order' => 1,
                'is_active' => true,
                'parent_id' => $bookings->id
            ],
            [
                'id' => '0d51b2d3-7d39-4362-9427-a04efea51332',
                'name' => 'Medical Package',
                'url' => '/cms/bookings/medical-package',
                'icon' => '',
                'order' => 2,
                'is_active' => true,
                'parent_id' => $bookings->id
            ],
            [
                'id' => '47adbfc3-aa23-4158-9ff9-86c392eca1eb',
                'name' => 'Send MCU Report',
                'url' => '/cms/bookings/send-mcu-report',
                'icon' => '',
                'order' => 3,
                'is_active' => true,
                'parent_id' => $bookings->id
            ],
            [
                'id' => '7a72a933-a475-40b6-b81a-11d5c5556e4e',
                'name' => 'Send Patient Invoice',
                'url' => '/cms/bookings/send-patient-invoice',
                'icon' => '',
                'order' => 4,
                'is_active' => true,
                'parent_id' => $bookings->id
            ],
            [
                'id' =>  '7cbf870e-d50f-4d97-8c87-1cfa1e3a6dae',
                'name' => 'General Payments',
                'url' => '/cms/general-payments',
                'icon' => 'ti ti-money',
                'order' => 0,
                'is_active' => true,
                'parent_id' => null
            ],
            [
                'id' =>  '83d2ca0b-9a53-4323-a178-1a3de92ca872',
                'name' => 'Trainings',
                'url' => '/cms/trainings',
                'icon' => '',
                'order' => 0,
                'is_active' => true,
                'parent_id' => null
            ],
            [
                'id' =>  'beea0f26-c7cc-4fcf-91b9-549f58b97e70',
                'name' => 'Inquiry Requests',
                'url' => '/cms/inquiry',
                'icon' => 'ti ti-files',
                'order' => 0,
                'is_active' => true,
                'parent_id' => null
            ],



        ];

        foreach ($data as $item) {
            \App\Models\Cms\Navigation::updateOrCreate([
                'id' => $item['id'],
                'name' => $item['name'],
            ], [
                'id' => $item['id'],
                'name' => $item['name'],
                'url' => $item['url'],
                'icon' => $item['icon'],
                'order' => $item['order'],
                'is_active' => $item['is_active'],
                'parent_id' => $item['parent_id'] ?? null
            ]);
        }
    }
}
