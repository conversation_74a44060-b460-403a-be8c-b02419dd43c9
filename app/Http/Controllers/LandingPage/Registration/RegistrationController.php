<?php

namespace App\Http\Controllers\LandingPage\Registration;

use App\Enums\Table\BihConfig\Group;
use App\Enums\Table\Otp\Method;
use App\Enums\Table\Otp\Platform;
use App\Enums\Table\Otp\Type;
use App\Enums\Table\Patient\PercentageProgress;
use App\Enums\Table\Patient\RelationPatient;
use App\Http\Controllers\Controller;
use App\Jobs\Notification\Discord\SendOTPNotification;
use App\Models\LandingPage\RegisterUser;
use App\Repositories\Config\BihConfigRepository;
use App\Repositories\CountryCode\CountryCodeRepository;
use App\Repositories\LandingPage\PublicUser\PublicUserRepository;
use App\Services\LandingPage\Patient\CreatePatientService;
use App\Services\LandingPage\Patient\SyncToSimrsService;
use App\Services\LandingPage\PublicUser\CreatePublicUserService;
use App\Services\LandingPage\PublicUser\GetByIdentityNumberPublicUserService;
use App\Services\LandingPage\PublicUser\GetByPhoneOrEmailPublicUserService;
use App\Services\LandingPage\RegisterUser\CreateRegisterUserService;
use App\Services\LandingPage\RegisterUser\DestroyRegisterUserService;
use App\Services\LandingPage\RegisterUser\GetByIDRegisterUserService;
use App\Services\LandingPage\RegisterUser\UpdateRegisterUserService;
use App\Services\OTP\OTPService\GetByIdOTPService;
use App\Services\OTP\OTPService\SendOTPService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class RegistrationController extends Controller
{
    protected CountryCodeRepository $countryCodeRepository;
    protected PublicUserRepository $publicUserRepository;
    protected BihConfigRepository $bihConfigRepository;

    public function __construct()
    {
        $this->countryCodeRepository = new CountryCodeRepository();
        $this->publicUserRepository = new PublicUserRepository();
        $this->bihConfigRepository = new BihConfigRepository();
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $countryCodes = $this->countryCodeRepository->getAllSortByExtension();
            $genders = $this->bihConfigRepository->getByGroup(Group::GENDER);
            $idCountryCode = $this->countryCodeRepository->getByExtension('62');

            $data = [
                'country_codes' => $countryCodes,
                'genders' => $genders,
                'id_country_code' => $idCountryCode
            ];

            return view('landing-page.registration.index', $data);
        } catch (\Throwable $th) {
            return back()->with('error', $th->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create() {}

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        /**
         * * ketika function store di-invoke dari route
         * * handle $request untuk divalidasi menggunakan method call()
         * * pada object yang dibuat dari class CreateRegisterUserService
         * * check validasi apa saja yang digunakan pada file App\Services\LandingPage\RegisterUser\CreateRegisterUserService;
         */
        try {

            $validator  = Validator::make($request->all(), [
                'firstName' => 'required|string|max:255',
                // 'lastName' => 'required|string|max:255',
                // 'identityType' => 'required|string|in:ktp,passport', // Adjust options as needed
                // 'identityNumber' => 'required|string|max:20', // Assuming identity numbers are unique
                // 'dateOfBirth' => 'required|date_format:d/m/Y',
                // 'sex' => 'required|string', // Add options as needed
                // 'countryCodeId' => 'required', // Assumes `countries` is the table name
                // 'phoneNumber' => 'required|string|max:20',
                'email' => 'required|string|email|max:255', // Assuming email needs to be unique in `users` table
                'isNewsletter' => 'nullable', // Allows `on` if checked, or null if unchecked
            ], [
                'firstName.required' => 'First name is required',
                // 'lastName.required' => 'Last name is required',
                // 'identityType.required' => 'Identity type is required',
                // 'identityType.in' => 'Identity type must be ktp or passport',
                // 'identityNumber.required' => 'Identity number is required',
                // 'dateOfBirth.required' => 'Date of birth is required',
                // 'dateOfBirth.date_format' => 'Date of birth must be in format d/m/Y',
                // 'sex.required' => 'Sex is required',
                // 'countryCodeId.required' => 'Country code is required',
                // 'phoneNumber.required' => 'Phone number is required',
                'email.required' => 'Email is required',
                'email.email' => 'Email must be a valid email address',
                'email.max' => 'Email must not exceed 255 characters',
            ]);
            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            $errors = [];
            if (@$request->email) {
                if (!validate_email($request->email)) {
                    $errors['email'] = 'Please enter a valid email address';
                    return back()->withErrors($errors)->withInput();
                }
            }
            if (empty(@$request->toggleLastName)) {
                if (empty($request->lastName)) {
                    $errors['lastName'] = 'Last name is required';
                    return back()->withErrors($errors)->withInput();
                }
            }

            // $checkDuplicateIdentityUserService = (new GetByIdentityNumberPublicUserService($request->all()))->call();
            // throw_if($checkDuplicateIdentityUserService->status() != 200, $checkDuplicateIdentityUserService->message());

            // if ($checkDuplicateIdentityUserService->data()) {
            //     $errors['identityNumber'] = 'This ID Number is already exist';
            // }

            // $phoneRequest = new Request();
            // $phoneRequest->replace(['phoneNumber' => $request->get('phoneNumber')]);
            // $checkPhoneExist = (new GetByPhoneOrEmailPublicUserService($phoneRequest->all()))->call();
            // throw_if($checkPhoneExist->status() != 200, $checkPhoneExist->message());

            // if ($checkPhoneExist->data()) {
            //     $errors['phoneNumber'] = 'This phone number is already exist';
            // }

            $emailRequest = new Request();
            $emailRequest->replace(['email' => $request->get('email')]);
            $checkEmailExist = (new GetByPhoneOrEmailPublicUserService($emailRequest->all()))->call();
            throw_if($checkEmailExist->status() != 200, $checkEmailExist->message());
            if ($checkEmailExist->data()) {
                $errors['email'] = 'This email is already exist';
                return back()->withErrors($errors)->withInput();
            }
            
            // if (count($errors) > 0) {
            //     return back()->withErrors($errors)->withInput();
            // }

            $service = (new CreateRegisterUserService($request->all()))->call();
            if ($service->status() == 422) return back()->withInput()->withErrors($service->data());
            throw_if($service->status() != 200, $service->message());

            $request->session()->put('registeredID', $service->data()->id);

            $registeredID = $request->session()->get('registeredID');
            if (!$registeredID) {
                return redirect()->route('registration.index');
            }

            $verificationService = (new GetByIDRegisterUserService($registeredID))->call();
            throw_if($verificationService->status() != 200, $verificationService->message());

            if (empty($verificationService->data())) {
                return redirect()->route('registration.index');
            }

            $verificationData = [
                'referenceId'    => $verificationService->data()->id,
                'contact'        => $verificationService->data()->email,
                'contactType'    => 2,
                // 'register_user'  => $verificationService->data(),
                'otpAbleType'   => RegisterUser::class,
                'otpType'       => Type::REGISTRATION,
                'platform'       => Platform::WEB,
            ];
            $verificationRequest = new Request($verificationData);
            $verificationResponse = $this->storeVerificationMethod($verificationRequest);
            return redirect()->route('registration.otp-verification.index');
        } catch (\Throwable $th) {

            return back()->withInput()->with('error', $th->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Show the form for choices of opt verification method.
     */
    public function getVerificationMethod(Request $request)
    {
        // dd($request->all());
        try {
            if (!$request->session()->has('registeredID')) {
                return redirect()->route('registration.index');
            }

            $registeredID = $request->session()->get('registeredID');
            $service = (new GetByIDRegisterUserService($registeredID))->call();
            throw_if($service->status() != 200, $service->message());

            if (empty($service->data())) {
                return redirect()->route('registration.index');
            }

            $data = [
                'register_user' => $service->data(),
                'otpable_type' => RegisterUser::class,
                'otp_type' => Type::REGISTRATION,
                'platform' => Platform::WEB,
            ];

            // return view('landing-page.registration.verification_method', $data);
        } catch (\Throwable $th) {
            return back()->with('error', $th->getMessage());
        }
    }

    /**
     * store a new request opt verification method.
     */
    public function storeVerificationMethod(Request $request): \Illuminate\Http\JsonResponse
    {
        // dd($request->all());
        // if ($request->ajax()) {
        try {
            $data = [];
            $referenceId = $request->input('referenceId');

            if (!empty($referenceId)) {
                $getRegisterUserService = (new GetByIDRegisterUserService($referenceId))->call();
                // dd($getRegisterUserService);
                throw_if($getRegisterUserService->status() != 200, $getRegisterUserService->message());
                $registerUser = $getRegisterUserService->data();
                $otpRetry = $registerUser->otp_retry;

                if ($otpRetry < (int)config('otp.otp_max_retry')) {
                    // send OTP request
                    $request->merge(['extension' => @$registerUser->countryCode->extension]);
                    $sendOTPService = (new SendOTPService($request->all()))->call();
                    // dd($sendOTPService);
                    // session()->forget('otpID');
                    // dd(session()->all());
                    throw_if($sendOTPService->status() != 200, $sendOTPService->message());

                    session()->put('otpID', $sendOTPService->data()->id);
                    // $request->session()->put('otpID', '55');
                    // $request->session()->put('otpID', $sendOTPService->data()->id);
                    // dd($sendOTPService);

                    $currentOTPRetry = $otpRetry + 1;
                    $updates = $currentOTPRetry == (int)config('otp.otp_max_retry') ? [
                        'otp_retry' => $currentOTPRetry,
                        'otp_lock_at' => Carbon::now()->setTimezone(config('app.timezone'))
                    ] : ['otp_retry' => $currentOTPRetry];

                    $updateRegisterUserService = (new UpdateRegisterUserService($updates, $referenceId))->call();
                    throw_if($updateRegisterUserService->status() != 200, $updateRegisterUserService->message());

                    // TODO:: remove these after got twillio and mailtrap paid account
                    // send message to discord channel
                    $message = "OTP FOR USER :";
                    if ($request->input('contactType') == Method::PHONE_NUMBER) {
                        $countryCode = $this->countryCodeRepository->getById($getRegisterUserService->data()->country_code_id);
                        $phoneWithExtension = "+" . $countryCode->extension . $request->input('contact');

                        $message .= "\nPhone : " . @$phoneWithExtension;
                    } else {
                        $message .= "\nEmail : " . @$request->input('contact');
                    }
                    $message .= "\nOTP : " . $sendOTPService->data()->value;
                    SendOTPNotification::dispatchSync($message);

                    $data = [
                        'otp' => $sendOTPService->data(),
                        'register_user' => $updateRegisterUserService->data()
                    ];
                }
            } else {
                return response()->json([
                    'success' => false,
                    'data' => [
                        'error' => "Registered ID is missing.",
                    ],
                    'message' => Response::$statusTexts[ResponseAlias::HTTP_BAD_REQUEST]
                ], ResponseAlias::HTTP_BAD_REQUEST);
            }

            return response()->json([
                'success' => true,
                'data' => $data,
                'message' => 'OTP has been created'
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'data' => [
                    'error' => $th->getMessage(),
                ],
                'message' => Response::$statusTexts[ResponseAlias::HTTP_INTERNAL_SERVER_ERROR]
            ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
        }
        // }

        return response()->json([
            'success' => false,
            'data' => [],
            'message' => 'Bad request'
        ]);
    }

    /**
     * Show the form for opt verification.
     */
    public function getOTPVerification(Request $request)
    {
        /**
         * * ketika function store di-invoke dari route
         * * handle $request untuk divalidasi menggunakan method call()
         * * pada object yang dibuat dari class GetByIDRegisterUserService and GetByIdOTPService
         * * check data register dan otp user yang digunakan pada file App\Services\LandingPage\RegisterUser\GetByIDRegisterUserService dan App\Services\OTP\GetByIdOTPService;
         */

        try {

            if (!$request->session()->has('registeredID')) {
                return redirect()->route('registration.index');
            }

            $registeredID = $request->session()->get('registeredID');
            $getByIdRegisterUserService = (new GetByIDRegisterUserService($registeredID))->call();
            throw_if($getByIdRegisterUserService->status() != 200, $getByIdRegisterUserService->message());
            $registerUser = $getByIdRegisterUserService->data();

            if (empty($registerUser)) {
                return redirect()->route('registration.index');
            }

            $attemptsLeft = (int)env('OTP_MAX_RETRY') - $registerUser->otp_retry;

            if (!$request->session()->has('otpID')) {
                return redirect()->route('registration.index');
            }

            $otpID = $request->session()->get('otpID');
            $getByIdOTPService = (new GetByIdOTPService($otpID))->call();
            throw_if($getByIdOTPService->status() != 200, $getByIdOTPService->message());
            $otp = $getByIdOTPService->data();

            if (empty($otp)) {
                return redirect()->route('registration.index');
            }

            $referenceContact = $otp->method == Method::PHONE_NUMBER ? $registerUser->phone : $registerUser->email;
            $referenceContactType = $otp->method == Method::PHONE_NUMBER ? Method::PHONE_NUMBER : Method::EMAIL;
            $data = [
                'reference_id' => $registerUser->id,
                'reference_contact' => $referenceContact,
                'reference_contact_type' => $referenceContactType,
                'otp_id' => $otp->id,
                'otpable_type' => $otp->otpable_type,
                'otp_type' => $otp->type,
                'otp_value' =>  $otp->value,
                'platform' => $otp->platform,
                'otp_duration'              => Carbon::parse($otp->expired_at),
                'otp_retry_duration'        => Carbon::parse($otp->expired_at),
                'attempts_left' => $attemptsLeft,
            ];

            return view('landing-page.registration.otp_verification', $data);
        } catch (\Throwable $th) {
            return back()->with('error', $th->getMessage());
        }
    }

    /**
     * reset otp attempts.
     */
    public function resetOTPAttempts(Request $request): \Illuminate\Http\JsonResponse
    {
        if ($request->ajax()) {
            try {

                $referenceId = $request->input('referenceId');

                if (!empty($referenceId)) {
                    $updateRegisterUserService = (new UpdateRegisterUserService(['otp_retry' => 0, 'otp_lock_at' => NULL], $request->input('referenceId')))->call();
                    throw_if($updateRegisterUserService->status() != 200, $updateRegisterUserService->message());

                    $data = [
                        'register_user' => $updateRegisterUserService->data()
                    ];
                } else {
                    return response()->json([
                        'success' => false,
                        'data' => [
                            'error' => "Registered ID is missing.",
                        ],
                        'message' => Response::$statusTexts[ResponseAlias::HTTP_BAD_REQUEST]
                    ], ResponseAlias::HTTP_BAD_REQUEST);
                }

                return response()->json([
                    'success' => true,
                    'data' => $data,
                    'message' => 'OTP has been reset successfully'
                ]);
            } catch (\Throwable $th) {
                return response()->json([
                    'success' => false,
                    'data' => [
                        'error' => $th->getMessage(),
                    ],
                    'message' => Response::$statusTexts[ResponseAlias::HTTP_INTERNAL_SERVER_ERROR]
                ], ResponseAlias::HTTP_INTERNAL_SERVER_ERROR);
            }
        }

        return response()->json([
            'success' => false,
            'data' => [],
            'message' => 'Bad request'
        ]);
    }

    public function storeOTPVerification(Request $request)
    {
        try {
            \DB::beginTransaction();

            $nowWithZone = Carbon::now()->setTimezone(config('app.timezone'));
            $referenceId = $request->input('referenceId');
            $referenceContactType = $request->input('referenceContactType');
            $otpId = $request->input('otpId');
            $otpNumber = implode('', $request->input('otp'));


            if (!empty($referenceId) && !empty($otpId)) {
                $getByIdOTPService = (new GetByIdOTPService($otpId))->call();
                throw_if($getByIdOTPService->status() != 200, $getByIdOTPService->message());
                $otp = $getByIdOTPService->data();

                if ($otp->value == (int)$otpNumber && $otp->expired_at > $nowWithZone) {
                    $updates = [
                        'email_verified_at' => $referenceContactType == "EMAIL" ? $nowWithZone : NULL,
                        'phone_verified_at' => $referenceContactType == "PHONE_NUMBER" ? $nowWithZone : NULL,
                        'otp_retry' => 0,
                        'otp_lock_at' => NULL
                    ];
                    $updateRegisterUserService = (new UpdateRegisterUserService($updates, $referenceId))->call();
                    throw_if($updateRegisterUserService->status() != 200, $updateRegisterUserService->message());

                    $getRegisterUserService = (new GetByIDRegisterUserService($referenceId))->call();
                    throw_if($getRegisterUserService->status() != 200, $getRegisterUserService->message());

                    $createPublicUserService = (new CreatePublicUserService($getRegisterUserService->data()))->call();
                    throw_if($createPublicUserService->status() != 200, $createPublicUserService->message());
                    // dd($createPublicUserService);

                    // create user data as a patient
                    $createPatientService = (new CreatePatientService([
                        'mr_no' => 0,
                        'public_user_id' => $createPublicUserService->data()->id,
                        'first_name' => $createPublicUserService->data()->first_name,
                        'last_name' => $createPublicUserService->data()->last_name,
                        'is_only_first_name' => $createPublicUserService->data()->is_only_first_name ?? false,
                        'fullname' => get_full_name($createPublicUserService->data()->first_name, $createPublicUserService->data()->last_name),
                        // 'contact_no' => $createPublicUserService->data()->phone,
                        // 'contact_country_code_id' => $createPublicUserService->data()->country_code_id,
                        // 'whatsapp_country_code_id' => $createPublicUserService->data()->country_code_id,
                        'email' => $createPublicUserService->data()->email,
                        // 'ktp_number' => $createPublicUserService->data()->ktp_number,
                        // 'passport_number' => $createPublicUserService->data()->passport_number,
                        // 'dob' => $createPublicUserService->data()->dob,
                        // 'gender' => $createPublicUserService->data()->gender,
                        'relation_patient' => RelationPatient::SELF,
                        'percentage_progress' => PercentageProgress::UNCOMPLETED_INFO
                    ]))->call();
                    throw_if($createPatientService->status() != 200, $createPatientService->message());
                    
                    $destroyRegisterUserService = (new DestroyRegisterUserService($referenceId))->call();
                    throw_if($destroyRegisterUserService->status() != 200, $destroyRegisterUserService->message());
                    
                    // $syncPatientToSimrsService = (new SyncToSimrsService($createPatientService->data()->id))->call();
                    // throw_if($syncPatientToSimrsService->status() != 200, 'Cannot create patient to simrs');
                    
                    // make user login
                    $publicUserId = $createPublicUserService->data()->id;
                    Auth::guard('public')->loginUsingId($publicUserId);
                    
                    // Store session for logged user
                    $request->session()->put('user_id', $createPublicUserService->data()->id);
                    $request->session()->put('user_name', $createPublicUserService->data()->fullname);
                    $request->session()->put('user_email', $createPublicUserService->data()->email);
                    // $request->session()->put('user_phone', $createPublicUserService->data()->phone);
                } else {
                    // dd('gagal');
                    \DB::rollBack();
                    return back()->withInput()->withErrors('OTP is not valid');
                }
            }

            \DB::commit();

            // return redirect()->route('landing-page.homepage');
            return redirect()->to(Session::get('redirectAfterLogin', route('landing-page.homepage')));
        } catch (\Throwable $th) {
            // dd($th->getMessage());
            \DB::rollBack();

            return back()->withInput()->with('error', $th->getMessage());
        }
    }
}
