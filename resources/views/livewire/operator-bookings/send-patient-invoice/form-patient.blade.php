<div class="flex flex-col gap-2 mb-2">
    <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 {{ $isOpenModalPatient ? '' : 'hidden' }}">
        <div class="sm:max-w-lg w-full m-3 sm:mx-auto">
            <div class="max-h-full overflow-hidden flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto p-6 mt-16 md:mt-28 xl:mt-0">
                <div class="flex justify-between items-center">
                    <span class="text-xl font-semibold">
                        Select Patient Profile
                    </span>
                    <div wire:click="onHandleCloseModal" class="flex justify-center items-center size-7 text-sm font-semibold rounded-full border border-transparent text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none hover:cursor-pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L12 10.9393L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L13.0607 12L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L12 13.0607L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L10.9393 12L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z" fill="#1D2939" stroke="#475467" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="md:h-[350px] mt-[20px]">
                    
                    <div class="flex flex-col gap-3 mt-8">
                        <div class="mb-4 px-2">
                            <input
                                type="text"
                                wire:model.live.debounce.500ms="search"
                                placeholder="Search patients by name..."
                                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0D4D8B]"
                            />
                        </div>
                        <div id="scrollingDropdown" class="overflow-y-auto h-[250px]">
                            @foreach($patients as $patient)
                                <div class="hover:cursor-pointer">
                                    <div id="card-patient-{{ $wireKey }}-{{ $patient->id }}" class="hover:cursor-pointer flex flex-row justify-between border rounded-xl p-4 items-center mt-1 hover:border-[#0D4D8B]"
                                        wire:click="onHandleSelectedPatient('{{ $patient->id }}')">
                                        <div class="flex flex-row gap-4 items-center">

                                            <div class="flex flex-col gap-1">
                                                <div class="flex gap-1 items-center">
                                                    @if($patient->verified_at || $patient->mr_no)
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                                                        </svg>
                                                    @endif
                                                    <span class="text-base font-light text-[#344054]">{{ $patient->fullname }}</span>
                                                </div>
                                                @if(@$patient->mr_no)
                                                    <div class="flex flex-row gap-1 text-[#667085] text-sm font-light">
                                                        <span>MR ID: </span>
                                                        <span>{{ @$patient->mr_no }}</span>
                                                    </div>
                                                @endif
                                                @if($patient->dob)
                                                    <span class="text-sm text-[#475467]">{{ \Carbon\Carbon::parse($patient->dob)->format('d F Y') }}</span>
                                                @endif
                                                <span class="text-sm text-[#475467]">{{ $patient->email }}</span>
                                                @if(!$patient->is_complete_data)
                                                    <div class="flex flex-row gap-2 py-1 px-3 bg-[#F79009] text-white items-center rounded-full hover:cursor-pointer">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M7 2.1875C4.34213 2.1875 2.1875 4.34213 2.1875 7C2.1875 9.65787 4.34213 11.8125 7 11.8125C9.65787 11.8125 11.8125 9.65787 11.8125 7C11.8125 4.34213 9.65787 2.1875 7 2.1875ZM1.3125 7C1.3125 3.85888 3.85888 1.3125 7 1.3125C10.1411 1.3125 12.6875 3.85888 12.6875 7C12.6875 10.1411 10.1411 12.6875 7 12.6875C3.85888 12.6875 1.3125 10.1411 1.3125 7ZM7 4.8125C7.24162 4.8125 7.4375 5.00838 7.4375 5.25V7.4375C7.4375 7.67912 7.24162 7.875 7 7.875C6.75838 7.875 6.5625 7.67912 6.5625 7.4375V5.25C6.5625 5.00838 6.75838 4.8125 7 4.8125ZM6.5625 9.1875C6.5625 8.94588 6.75838 8.75 7 8.75H7.00437C7.246 8.75 7.44187 8.94588 7.44187 9.1875V9.19187C7.44187 9.4335 7.246 9.62937 7.00437 9.62937H7C6.75838 9.62937 6.5625 9.4335 6.5625 9.19187V9.1875Z" fill="white"/>
                                                        </svg>
                                                        <span class="text-xs lg:text-sm font-light">Data is incomplete</span>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="flex flex-row items-center gap-2">
                                            
                                            <div class="h-4 w-4 bg-[#F2F4F7] rounded-full hover:cursor-pointer" id="self-id-inactive-{{ $wireKey }}-{{$patient->id}}">
                                            </div>
                                            <div class="hidden" id="self-id-active-{{$wireKey}}-{{$patient->id}}">
                                                <div class="h-4 w-4 bg-[#0D4D8B] rounded-full hover:cursor-pointer flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none" class="">
                                                        <path d="M8.33268 2.5L3.74935 7.08333L1.66602 5" stroke="white" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        <div id="loader" class="hidden">
                            <div class="flex justify-center items-center my-1">
                                <svg class="animate-spin h-8 w-8 text-[#0D4D8B]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>

                    </div>
                </div>
                <div wire:click="continue" class="py-4 bg-[#0D4D8B] flex flex-row gap-3
                        rounded-xl justify-center hover:cursor-pointer mt-8" data-hs-overlay="#hs-scroll-inside-body-modal">
                    <span class="text-white text-base font-medium rounded-xl">Select</span>
                </div>
            </div>
        </div>
    </div>
    <div class="flex flex-row gap-1 items-center">
        <span>Patient Profile</span>
        <span class="text-red-500">*</span>
    </div>
    @if($isSelectedPatient)
        <div class="border rounded-xl p-4 items-center bg-white">
            <div class="flex flex-col gap-1 lg:gap-0 lg:flex-row justify-between">
                <div class="flex flex-col">
                    <span class="text-[#667085] text-base font-medium">Others</span>
                    <div class="flex flex-row gap-4 items-center">
                        <div class="flex flex-col gap-1">
                            <div class="flex gap-1 items-center">
                                @if($selectedPatient->verified_at || $selectedPatient->mr_no)
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                                    </svg>
                                @endif
                                <span class="text-base font-light text-[#344054]">{{ $selectedPatient->fullname }}</span>
                                
                            </div>
                            @if(@$selectedPatient->mr_no)
                                <div class="flex flex-row gap-1 text-[#667085] text-sm font-light">
                                    <span>MR ID: </span>
                                    <span>{{ @$selectedPatient->mr_no }}</span>
                                </div>
                            @endif
                            @if($selectedPatient->dob)
                                <span class="text-sm text-[#475467]">{{ \Carbon\Carbon::parse($selectedPatient->dob)->format('d F Y') }}</span>
                            @endif
                            @if($selectedPatient->email)
                                <span class="text-sm text-[#475467]">{{ $selectedPatient->email ?? '-' }}</span>
                            @endif
                            
                        </div>
                    </div>
                </div>
                <div wire:click="onHandleSelectProfile" class="flex flex-row items-center gap-1 hover:cursor-pointer">
                    <span class="text-base font-medium text-[#0D4D8B]">Change Profile</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.21967 5.21967C8.51256 4.92678 8.98744 4.92678 9.28033 5.21967L13.5303 9.46967C13.8232 9.76256 13.8232 10.2374 13.5303 10.5303L9.28033 14.7803C8.98744 15.0732 8.51256 15.0732 8.21967 14.7803C7.92678 14.4874 7.92678 14.0126 8.21967 13.7197L11.9393 10L8.21967 6.28033C7.92678 5.98744 7.92678 5.51256 8.21967 5.21967Z" fill="#8FC640"/>
                    </svg>
                </div>
            </div>
        </div>
    @else
        <div wire:click="onHandleSelectProfile" class="rounded-xl border flex flex-row
            justify-between p-4 items-center hover:cursor-pointer bg-white {{ Session::has('error_selected_patient') ? 'border-red-500' : '' }}">
            <div class="flex-row flex gap-4 items-center">
                
                <span class="text-base font-light text-[Gray/600]">Select Profile</span>
            </div>
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M8.21967 5.21967C8.51256 4.92678 8.98744 4.92678 9.28033 5.21967L13.5303 9.46967C13.8232 9.76256 13.8232 10.2374 13.5303 10.5303L9.28033 14.7803C8.98744 15.0732 8.51256 15.0732 8.21967 14.7803C7.92678 14.4874 7.92678 14.0126 8.21967 13.7197L11.9393 10L8.21967 6.28033C7.92678 5.98744 7.92678 5.51256 8.21967 5.21967Z" fill="#8FC640"/>
            </svg>
        </div>
        @if(Session::has('error_selected_patient'))
            <div class="text-xs text-red-500">{{ Session::get('error_selected_patient') }}</div>
        @endif
    @endif

</div>

@script
<script>
    $wire.on('medical-package-patient-selected', (event) => {
        console.log(event);
        event['allPatientIds'].forEach(function (id) {
            if(id === event['id']){
                return;
            }

            document.getElementById('self-id-active-'+event['wireKey']+'-'+id).style.display = 'none';
            document.getElementById('self-id-inactive-'+event['wireKey']+'-'+id).style.display = 'block';
            var card = document.getElementById("card-patient-"+event['wireKey']+'-'+id);
            card.className = "flex flex-row justify-between border rounded-xl p-4 items-center hover:border-[#0D4D8B]";
        });
        document.getElementById('self-id-active-'+event['wireKey']+'-'+event['id']).style.display = 'block';
        document.getElementById('self-id-inactive-'+event['wireKey']+'-'+event['id']).style.display = 'none';
        var card = document.getElementById("card-patient-"+event['wireKey']+'-'+event['id']);
        card.className = "flex flex-row justify-between border rounded-xl p-4 items-center border-[#0D4D8B] bg-[#E5F2FF]";
    });

    window.onload = function() {
        // Scroll to the top of the page
        window.scrollTo(0, 0);
    };
</script>
@endscript


@script
    <script>
        const listContainer = document.getElementById('scrollingDropdown');
        const loaderElement = document.getElementById('loader');

        listContainer.addEventListener('scroll', function () {
            if (listContainer.scrollTop + listContainer.clientHeight >= listContainer.scrollHeight) {
                loaderElement.classList.remove('hidden');
                $dispatch('loadMore');
            }
        });
    </script>
@endscript