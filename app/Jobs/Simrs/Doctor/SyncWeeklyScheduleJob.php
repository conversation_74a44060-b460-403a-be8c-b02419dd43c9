<?php

namespace App\Jobs\Simrs\Doctor;

use App\Enums\Table\DoctorRegularSchedule\Day;
use App\Models\Doctor;
use App\Models\DoctorRegularSchedule;
use App\Repositories\Simrs\SimrsRepository;
use App\Services\Simrs\Doctor\Schedule\WeeklyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncWeeklyScheduleJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected SimrsRepository $simrsRepository;

    /**
     * Create a new job instance.
     * $id -> doctors.id
     */
    public function __construct(protected int $id)
    {
        $this->onQueue('request-trakcare-queue');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {

            $doctor = Doctor::find($this->id);

            $data = [
                'doctor_id' => $doctor->simrs_doctor_id,
            ];

            $weeklyService = (new WeeklyService($data, true, false))->call();
            if($weeklyService->data()){

                foreach ($weeklyService->data() as $schedule){
                    $day = Day::getDayId($schedule['day']);
                    $doctorRegularSchedule = DoctorRegularSchedule::updateOrCreate([
                        'day'           => $day,
                        'doctor_id'     => $doctor->id,
                    ],[
                        'time_from'     => $schedule['timefrom'],
                        'time_to'       => $schedule['timeto'],
                        'location'      => $schedule['clinic_desc'],
                    ]);

                    $affectedIds[]      = $doctorRegularSchedule->id;
                }
                // delete unused schedule
                DoctorRegularSchedule::where('doctor_id', $doctor->id)
                    ->whereNotIn('id', $affectedIds)
                    ->delete();
            }

        }catch (\Exception $e){

            report($e);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $e->getMessage(),
                'On file ' => $e->getFile(),
                'On line ' => $e->getLine()
            ]);

            $this->fail(json_encode($e));

        }
    }

    public function tags(): array
    {
        return ['DOCTOR ID ' . $this->id];
    }
}
