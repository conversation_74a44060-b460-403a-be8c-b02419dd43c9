@extends('layouts.admin')

@section('title', 'Page 2')

@section('css')
    <link rel="stylesheet" href="{{ asset('/cms-assets/vendor/libs/node-waves/node-waves.css') }}" />
    <link rel="stylesheet" href="{{ asset('/cms-assets/vendor/libs/tagify/tagify.css') }}" />
    <link rel="stylesheet" href="{{ asset('/cms-assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('/cms-assets/vendor/libs/bootstrap-select/bootstrap-select.css') }}" />
    <link rel="stylesheet" href="{{ asset('/cms-assets/vendor/libs/quill/katex.css') }}" />
    <link rel="stylesheet" href="{{ asset('/cms-assets/vendor/libs/quill/editor.css') }}" />
    <link rel="stylesheet" href="{{ asset('/cms-assets/vendor/libs/typeahead-js/typeahead.css') }}" />
    <link rel="stylesheet" href="{{ asset('/cms-assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css') }}" />


    <style>
        .file-upload {
            text-align: center;
            border: 3px dashed rgb(210, 227, 244);
            padding: 1.5rem;
            position: relative;
            cursor: pointer;
        }

        .file-upload p {
            font-size: 0.87rem;
            margin-top: 10px;
            color: #bbcada;
        }

        .file-upload input {
            display: block;
            height: 100%;
            width: 100%;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            opacity: 0;
            cursor: pointer;
        }
    </style>
@endsection

@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        <h4 class="py-3 mb-4"><span class="text-muted fw-light">Package /</span> Edit</h4>
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <h3 class="card-header">Package Edit</h3>
                    <div class="card-body">
                        @include('alert')
                        <div class="mb-3">
                            <form id="from-input" method="POST" action="{{ route('cms.packages.update', $package->uuid) }}"
                                  enctype="multipart/form-data" class="needs-validation" novalidate>
                                @csrf
                                @method('PUT')

                                <input type="hidden" name="id" value="{{ $package->id }}" />
                                <input type="hidden" name="uuid" value="{{ $package->uuid }}" />

                                <div class="mb-3">
                                    <label for="title" class="form-label">Package Name</label>
                                    <input type="text" class="form-control" id="title" name="title"
                                           value="{{ old('title') ?? $package->title }}" required disabled>
                                    <div class="valid-feedback"></div>
                                    <div class="invalid-feedback">Please enter title.</div>
                                </div>

                                <div class="mb-3 row">
                                    <div class="col-md-6">
                                        <span class="switch-label">Is Published</span>
                                        <label class="switch switch-lg">
                                            <input type="checkbox" class="switch-input" name="is_published"
                                                {{ $package->is_published ? 'checked' : '' }}>
                                            <span class="switch-toggle-slider">
                                                <span class="switch-on">
                                                    <i class="ti ti-check"></i>
                                                </span>
                                                <span class="switch-off">
                                                    <i class="ti ti-x"></i>
                                                </span>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="col-md-6">
                                        <span class="switch-label">Is Recommended</span>
                                        <label class="switch switch-lg">
                                            <input type="checkbox" class="switch-input" name="is_recommended"
                                                {{ $package->is_recommended ? 'checked' : '' }}>
                                            <span class="switch-toggle-slider">
                                                <span class="switch-on">
                                                    <i class="ti ti-check"></i>
                                                </span>
                                                <span class="switch-off">
                                                    <i class="ti ti-x"></i>
                                                </span>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3 row">
                                    <div class="col-md-6">
                                        <label for="package_type_id" class="form-label">Type Name</label>
                                        <select id="package_type_id" class="select2-type form-control"
                                                data-style="btn-default" data-live-search="true" name="package_type_id" required
                                                disabled>
                                            <option value="">Select Package Type</option>
                                            @foreach ($packageTypes as $packageType)
                                                <option value="{{ $packageType->id }}"
                                                        @if ($packageType->id == $package->package_type_id) selected @endif>
                                                {{ $packageType->name }}
                                            @endforeach
                                        </select>
                                        <div class="valid-feedback"></div>
                                        <div class="invalid-feedback">Please select a package type.</div>
                                    </div>

                                    <div class="col-md-6">
                                        <label for="package_category_id" class="form-label">Category Name</label>
                                        <select id="package_category_id" class="select2-category form-control"
                                                name="package_category_id" required disabled>
                                            <option value="">Select Package Category</option>
                                            @foreach ($packageCategories as $packageCategory)
                                                <option value="{{ $packageCategory->id }}"
                                                        @if ($packageCategory->id == $package->package_category_id) selected @endif>
                                                {{ $packageCategory->name }}
                                            @endforeach
                                        </select>
                                        <div class="valid-feedback"></div>
                                        <div class="invalid-feedback">Please select a package category.</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="image" class="form-label">Image Content</label> <span
                                        class="text-danger">*</span> &nbsp; &nbsp; &nbsp;

                                    <div class="input-group">
                                        <input type="file" id="image" name="image" accept='image/*'
                                               class="form-control" @if ($package->image == null || $package->image == '') required @endif />
                                        <a href="{{ $package->image }}" class="btn btn-outline-primary"
                                           id="inputGroupFileAddon04" target="_blank">View Image</a>
                                    </div>

                                    <span class="text-small text-muted">Max file size {{max_file_upload_in_mb()}}MB. Recommended resolution 1200x675
                                        (ratio 16:9).</span>
                                    <div class="valid-feedback"></div>
                                    <div class="invalid-feedback">Please enter image content.</div>

                                </div>

                                <div class="mb-3">
                                    <label for="glance" class="form-label">At a Glance</label>
                                    <div class="form-repeater-glance">
                                        <div data-repeater-list="glance">
                                            <div data>
                                            </div>
                                            <div data-repeater-item>
                                                <div class="row">
                                                    <div class="mb-3 col-md-4 mb-0">
                                                        <label class="form-label" for="form-repeater">icon</label>
                                                        <div class="input-group">
                                                            <input type="file" id="glance-icon" name="glance-icon"
                                                                   accept='image/*' class="form-control" />
                                                            {{-- <a href="javascript:void(0)" class="btn btn-outline-primary"
                                                                    id="glance-icon-button" target="_blank">View</a> --}}
                                                        </div>
                                                    </div>
                                                    <div class="mb-3 col-md-6 mb-0">
                                                        <label class="form-label" for="form-repeater">Item Name</label>
                                                        <input type="text" id="glance-item" class="form-control"
                                                               required name="glance-item" placeholder="item name" />
                                                    </div>

                                                    <div class="mb-3 col-md-2 d-flex align-items-center mb-0">

                                                        <span class="btn btn-label-danger mt-4" data-repeater-delete>
                                                            <i class="ti ti-trash ti-xs me-1"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-0">
                                            <button class="btn btn-primary" data-repeater-create>
                                                <i class="ti ti-plus me-1"></i>
                                                <span class="align-middle">Add</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="Inclusion" class="form-label">Package Inclusion</label>
                                    <div class="form-control" style="background-color: rgba(75, 70, 92, 0.08);" >
                                        @foreach ($inclusions as $inclusion)
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <h5>{{ $inclusion['name'] }}</h5>
                                                    <ul>
                                                        @foreach ($inclusion['abbrivations'] as $abbrivation)
                                                            <li>{{ $abbrivation }}</li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="content" class="form-label">Description</label>
                                    <textarea class="form-control" id="content" name="content" required disabled rows="6">{{ $package->content }}</textarea>
                                    {{-- <input type="text" class="form-control" id="title" name="title"
                                        value="{{ old('title') ?? $package->title }}" required disabled>
                                    <div class="valid-feedback"></div>
                                    <div class="invalid-feedback">Please enter title.</div> --}}
                                </div>

                                <div class="mb-3 row">
                                    <div class="col-md-6">
                                        <label for="preparation" class="form-label">Preparation</label>
                                        <textarea class="form-control" id="preparations" name="preparations" rows="6">{{ $package->preparations }}</textarea>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="term_conditions" class="form-label">Terms & Condition</label>
                                        <textarea class="form-control" id="term_conditions" name="term_conditions" rows="6">{{ $package->term_conditions }}</textarea>
                                    </div>
                                </div>

                                <div class="mb-3 row">
                                    <div class="col-md-4">
                                        <label for="preparation" class="form-label">Location</label>
                                        <input type="text" class="form-control" id="location" name="location"
                                               value="{{ old('location') ?? $package->location }}">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="preparation" class="form-label">Building & Floor</label>
                                        <input type="text" class="form-control" id="building_floor"
                                               name="building_floor"
                                               value="{{ old('building_floor') ?? $package->building_floor }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="preparation" class="form-label">Open at</label>
                                        <input type="time" class="form-control" id="open_at" name="open_at"
                                               value="{{ old('open_at') ?? $package->open_at }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="preparation" class="form-label">Closed at</label>
                                        <input type="time" class="form-control" id="close_at" name="close_at"
                                               value="{{ old('close_at') ?? $package->close_at }}">
                                    </div>
                                </div>



                                <div class="mb-3">
                                    <label for="price" class="form-label">Price </label><span
                                        class="text-danger">*</span>
                                    <div class="input-group">
                                        <span class="btn btn-outline">Rp</span>
                                        <input type="number" class="form-control" id="price" name="price"
                                               value="{{ old('price') ?? $package->price }}" required disabled>

                                    </div>


                                    <div class="valid-feedback"></div>
                                    <div class="invalid-feedback">Please enter price.</div>
                                </div>
                                
                                @if(featureFlag('reschedule_mcu_booking'))
                                <div class="mb-3 row">
                                    <div class="col-md-4">
                                        <label for="reschedule_policy" class="form-label">Reschedule Policy</label>
                                        <select id="reschedule_policy" class="select2-type form-control"
                                                data-style="btn-default" data-live-search="true" name="reschedule_policy" required
                                                >
                                            <option value="">Select Policy</option>
                                            @foreach ($reschedulePolicies as $key => $reschedulePolicy)
                                                <option value="{{ $reschedulePolicy['value'] }}"
                                                        @if ($reschedulePolicy['value'] == $package->reschedule_policy) selected @endif>
                                                {{ $reschedulePolicy['label'] }}
                                            @endforeach
                                        </select>
                                        <div class="valid-feedback"></div>
                                        <div class="invalid-feedback">Please select a policy.</div>
                                    </div>

                                    <div class="col-md-2" id="comp-reschedule-period">
                                        <label for="preparation" class="form-label">Reschedule Period</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="reschedule_period" name="reschedule_period"
                                                value="{{ old('reschedule_period') ?? $package->reschedule_period }}">
                                            <span class="btn btn-outline">Days</span>
                                        </div>
                                    </div>

                                    <div class="col-md-3" id="comp-reschedule-period-with-promocode">
                                        <label for="preparation" class="form-label">Reschedule Period (with promocode)</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="reschedule_period_with_promocode" name="reschedule_period_with_promocode"
                                                value="{{ old('reschedule_period_with_promocode') ?? $package->reschedule_period_with_promocode }}">
                                            <span class="btn btn-outline">Days</span>
                                        </div>
                                    </div>

                                    <div class="col-md-3" id="comp-reschedule-to-visit-period">
                                        <label for="preparation" class="form-label">Reschedule To Visit Period</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="reschedule_to_visit_period" name="reschedule_to_visit_period"
                                                value="{{ old('reschedule_to_visit_period') ?? $package->reschedule_to_visit_period }}">
                                            <span class="btn btn-outline">Hours</span>
                                        </div>
                                    </div>

                                </div>
                                @endif

                                <div class="float-end">
                                    <a href="{{ route('cms.packages') }}" class="btn btn-link">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Submit</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('js')
    <script src="{{ asset('/cms-assets/vendor/libs/typeahead-js/typeahead.js') }}"></script>
    <script src="{{ asset('/cms-assets/vendor/libs/tagify/tagify.js') }}"></script>
    <script src="{{ asset('/cms-assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('/cms-assets/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
    <script src="{{ asset('/cms-assets/vendor/libs/quill/katex.js') }}"></script>
    <script src="{{ asset('/cms-assets/vendor/libs/quill/quill.js') }}"></script>
    <script src="{{ asset('/cms-assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js') }}"></script>
    <script src="{{ asset('/cms-assets/vendor/libs/jquery-repeater/jquery-repeater.js') }}"></script>

    <script>
        $(document).ready(function() {
            // $('.select2-type').select2();
            // $('#package_category_id').select2();

            // $('#expired_at').datepicker({
            //     format: 'yyyy-mm-dd'
            // });

            $('#reschedule_policy').select2({
                placeholder: 'Select Policy',
            });

            function handleReschedulePolicyChange() {
                const selectedValue = $('#reschedule_policy').val();
                if (selectedValue == "not-allowed") {
                    $('#comp-reschedule-period').hide();
                    $('#comp-reschedule-period-with-promocode').hide();
                    $('#comp-reschedule-to-visit-period').hide();
                } else if (selectedValue == "indefinitely") {
                    $('#comp-reschedule-period').hide();
                    $('#comp-reschedule-period-with-promocode').hide();
                    $('#comp-reschedule-to-visit-period').show();
                } else {
                    $('#comp-reschedule-period').show();
                    $('#comp-reschedule-period-with-promocode').show();
                    $('#comp-reschedule-to-visit-period').show();
                }
            }

            $('#reschedule_policy').on('change', handleReschedulePolicyChange);
            handleReschedulePolicyChange();

            function initializeRepeater() {
                var $formRepeaterGlance = $('.form-repeater-glance').repeater({
                    initEmpty: true,
                    show: function() {
                        $(this).slideDown();
                    },
                    hide: function(e) {
                        confirm('Are you sure you want to delete this element?') && $(this).slideUp(e);
                    }
                });

                if (typeof $formRepeaterGlance.setList === 'function') {
                    @if ($package->glance->count() > 0)
                    $formRepeaterGlance.setList([
                            @foreach ($package->glance as $glance)
                        {
                            'glance-item': '{{ $glance->name }}',
                            'glance-icon-button': '{{ $glance->icon }}',
                        },
                        @endforeach
                    ]);
                    @endif
                } else {
                    console.error('setList function is not available, retrying initialization.');
                    setTimeout(initializeRepeater, 500); // Retry after 500ms
                }
            }

            initializeRepeater();


            // const standartToolbar = [
            //     [{
            //         size: []
            //     }],
            //     ['bold', 'italic', 'underline', 'strike'],
            //     [{
            //             list: 'ordered'
            //         },
            //         {
            //             list: 'bullet'
            //         },
            //     ],
            // ];
            // var quill = new Quill('#quill_editor', {
            //     theme: 'snow',
            //     bounds: '#quill_editor',
            //     placeholder: 'Type Something...',
            //     modules: {
            //         formula: true,
            //         toolbar: standartToolbar
            //     },
            // });

            // quill.on('text-change', function(delta, oldDelta, source) {
            //     var quill_value = quill.container.firstChild.innerHTML
            //     // console.log(quill_value);
            //     $("#content").val(quill_value);
            // });

            // $("#from-input").submit(function() {
            //     var content = $("#content").val();
            //     if (content == null || content == "" || content == "<p><br></p>" || content ==
            //         "<p><br></p><p><br></p>" || content == "<p><br></p><p><br></p><p><br></p>") {
            //         $("#content").attr("required", true);
            //         return false;
            //     }
            // });



            $("#image").change(function() {
                var input = this;
                var maxFileSize = {{ config('filesystems.max_file_size') }};
                if (input.files && input.files[0]) {

                    if (input.files[0].size > maxFileSize) {
                        alert("File size is too large. Max file size is {{max_file_upload_in_mb()}}mb");
                        input.value = null;
                        return;
                    }
                }
            });


            // $('.select2-type').on('change', function(e) {

            //     var $option = $(this).find('option:selected');
            //     var id = $(this).val();
            //     $.ajax({
            //         url: "{{ route('cms.packages.get-package-categories') }}",
            //         type: 'GET',
            //         data: {
            //             id: id
            //         },
            //         success: function(response) {
            //             // console.log(response);
            //             $('#package_category_id').empty();
            //             // $('#package_category_id').select2();
            //             $('#package_category_id').append(
            //                 '<option value=""> -- Select -- </option>');
            //             $.each(response, function(key, value) {
            //                 $('#package_category_id').append('<option value="' + value
            //                     .id + '">' + value.name +
            //                     '</option>');
            //             });


            //         },
            //         error: function(response) {
            //             // console.log(response);
            //         },
            //     });
            // })
        });
    </script>
@endsection
