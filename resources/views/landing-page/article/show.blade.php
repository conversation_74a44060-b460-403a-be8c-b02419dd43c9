@section('title', 'Bali International Hospital')
@extends('landing-page.layouts.masterTemplate')
@php
    $platform = session()->get('platform');
@endphp
<head>
    <title>{{ $article->title }}</title>

    <meta name="description" content="{{ $article->seo_description }}">
    <meta name="author" content="Bali International Hospital">
    <meta name="keywords" content="{{ str_replace('-', ',', $article->title) }}">

    <meta property="og:type" content="article">
    <meta property="og:title" content="{{ $article->seo_title }}">
    <meta property="og:description" content="{{ $article->seo_description }}">
    <meta property="og:url" content="{{ request()->url() }}">
    <meta property="og:image" content="{{ asset_gcs($article->seo_image) }}">
    <link href="https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.snow.css" rel="stylesheet" />
    <link href="https://cdn.quilljs.com/1.3.7/quill.snow.css" rel="stylesheet">
    <link href="https://cdn.quilljs.com/1.3.7/quill.bubble.css" rel="stylesheet">


    <style>
        /* Remove border from the editor */
        .ql-container {
            border: none !important;
        }
    </style>
    @include('landing-page.partials.bg_move_style_screen')
</head>

@section('content')

    <div class="movingColors hidden lg:block">
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
    </div>
    <div class="h-20">
    </div>
    @if(!in_array($platform, ['mobile']))
        <div class="relative mx-4 xl:mx-40 flex flex-row py-4 gap-4 items-center z-10 md:mt-10 mt-4">
            <a href="{{ route('articles.index') }}" class="text-blue-800">Blog & News</a>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span> {{ $article->title }} </span>
        </div>
    @endif

    <div class="mx-4 xl:mx-40 mt-6 relative">
        <div class="flex flex-col gap-3 relative z-2">
            <div class="flex flex-row">
                <div class="flex flex-row gap-4 items-center">
                    @if(@$article->user->image)
                        <img src="{{ @$article->user->image }}" alt="Bali International Hospital"
                             class="inset-0 object-cover h-6 rounded-full w-6"
                             style="background: radial-gradient(77.36% 77.36% at 45.71% 50%, rgba(7, 39, 70, 0.33) 0%, rgba(7, 39, 70, 0.72) 100%);">
                    @else
                        <div class="rounded-full bg-[#f7faff] items-center flex justify-center p-2">
                            <span class="text-[10px] lg:text-xs font-bold text-gray-700">{{ @$article->user->initials }}</span>
                        </div>
                    @endif
                    <span class="text-sm">{{ $article->user->name }}</span>
                </div>
            </div>
            <h1 class="text-2xl font-semibold">{{ $article->title }}</h1>
            <div class="flex flex-wrap xl:flex-row gap-4">
                <div class="flex flex-row gap-3 items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M12 2.25C6.61522 2.25 2.25 6.61522 2.25 12C2.25 17.3848 6.61522 21.75 12 21.75C17.3848 21.75 21.75 17.3848 21.75 12C21.75 6.61522 17.3848 2.25 12 2.25ZM12.75 6C12.75 5.58579 12.4142 5.25 12 5.25C11.5858 5.25 11.25 5.58579 11.25 6V12C11.25 12.4142 11.5858 12.75 12 12.75H16.5C16.9142 12.75 17.25 12.4142 17.25 12C17.25 11.5858 16.9142 11.25 16.5 11.25H12.75V6Z" fill="#667085"/>
                    </svg>
                    <div class="flex gap-1 items-center text-gray-500 text-sm">
                        <span>{{ $article->time_to_read == 0 ? 5 : $article->time_to_read}} min read</span>
                        <span>.</span>
                        <span>{{ \Carbon\Carbon::parse($article->created_at)->format('d M Y') }}</span>
                    </div>
                </div>
                <div class="flex flex-row gap-3 items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z" fill="#667085"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.664255 10.5904C0.517392 10.2087 0.517518 9.78563 0.66461 9.40408C2.10878 5.65788 5.7433 3 9.99859 3C14.256 3 17.892 5.66051 19.3347 9.40962C19.4816 9.79127 19.4814 10.2144 19.3344 10.5959C17.8902 14.3421 14.2557 17 10.0004 17C5.74298 17 2.10698 14.3395 0.664255 10.5904ZM14.0004 10C14.0004 12.2091 12.2095 14 10.0004 14C7.79123 14 6.00037 12.2091 6.00037 10C6.00037 7.79086 7.79123 6 10.0004 6C12.2095 6 14.0004 7.79086 14.0004 10Z" fill="#667085"/>
                    </svg>
                    <div class="flex gap-1 items-center text-gray-500 text-sm">
                        <span>{{ $article->articleReadActivities->count() }} views</span>
                    </div>
                </div>
                <div class="rounded-full border border-green-500 text-green-500 bg-white px-4 hover:cursor-pointer">
                    <span class="text-sm">{{ @$article->category->name }}</span>
                </div>
            </div>
            <div class="flex flex-col xl:flex-row gap-4">
                <div class="flex flex-col xl:w-3/5 gap-10">
                    <img src="{{ asset_gcs($article->image) }}" alt="" class="rounded-xl">
                    <div class="ql-container ql-snow">
                        <div class="ql-editor">
                            {!! $article->content !!}
                        </div>
                    </div>
                </div>
                <div class="flex flex-col xl:w-2/5 gap-8">

                    @if($article->doctors->count() > 0)
                        <div class="flex flex-col gap-4">
                            <span class="text-xl font-semibold">Relevant Medical Expert</span>
                            @foreach($article->doctors as $doctor)
                                <div class="flex flex-col p-4 rounded-3xl shadow-md gap-4 bg-white">
                                    <div class="flex flex-row gap-4 items-center">
                                        <div class="relative">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none" class="absolute -bottom-0 -right-0">
                                                <path d="M8.76743 20.0097L9.46485 19.3837L8.76743 20.0097C9.95952 21.3379 12.0405 21.3379 13.2326 20.0097L13.3557 19.8725C13.5951 19.6058 13.9589 19.4876 14.3093 19.5627L14.4896 19.6013C16.2347 19.9751 17.9182 18.7519 18.102 16.9768L18.121 16.7934C18.1579 16.4369 18.3827 16.1274 18.7103 15.9822L18.8789 15.9075C20.5104 15.1841 21.1534 13.205 20.2587 11.6609L20.1663 11.5014C19.9866 11.1913 19.9866 10.8087 20.1663 10.4986L20.2587 10.3391C21.1534 8.79497 20.5104 6.81587 18.8789 6.09253L18.7103 6.01782C18.3827 5.87256 18.1579 5.56309 18.121 5.20661L18.102 5.02324C17.9182 3.24806 16.2347 2.02491 14.4896 2.39869L14.3093 2.4373C13.9589 2.51237 13.5951 2.39416 13.3557 2.12745L13.2326 1.99026C12.0405 0.662127 9.95952 0.66213 8.76743 1.99026L8.64429 2.12745L9.38848 2.79542L8.64429 2.12745C8.4049 2.39416 8.04109 2.51237 7.69066 2.4373L7.5104 2.39869C5.76531 2.02491 4.08179 3.24806 3.89802 5.02324L3.87904 5.20661C3.84213 5.56309 3.61729 5.87256 3.28966 6.01782L3.69496 6.932L3.28966 6.01782L3.12113 6.09253C1.48962 6.81587 0.846576 8.79497 1.74133 10.3391L1.83375 10.4986C2.01343 10.8087 2.01343 11.1913 1.83375 11.5014L2.69899 12.0027L1.83375 11.5014L1.74133 11.6609C0.846574 13.205 1.48963 15.1841 3.12113 15.9075L3.28966 15.9822L3.69496 15.068L3.28966 15.9822C3.61729 16.1274 3.84213 16.4369 3.87904 16.7934L3.89802 16.9768C4.08179 18.7519 5.76532 19.9751 7.5104 19.6013L7.30096 18.6235L7.51041 19.6013L7.69065 19.5627C8.04109 19.4876 8.4049 19.6058 8.64429 19.8725L8.76743 20.0097Z" fill="url(#paint0_linear_2803_80113)" stroke="white" stroke-width="2"/>
                                                <path d="M10.5584 7.33142C10.7464 6.97746 11.2536 6.97746 11.4416 7.33142L12.2662 8.88399C12.3385 9.02007 12.4694 9.11522 12.6212 9.1419L14.3526 9.44638C14.7473 9.51579 14.904 9.99814 14.6255 10.2863L13.4037 11.5503C13.2966 11.6611 13.2466 11.8151 13.2681 11.9676L13.5136 13.7084C13.5695 14.1052 13.1592 14.4034 12.7991 14.2275L11.2194 13.4561C11.0809 13.3885 10.9191 13.3885 10.7806 13.4561L9.20091 14.2275C8.84077 14.4034 8.43045 14.1052 8.48641 13.7084L8.73188 11.9676C8.75339 11.8151 8.70337 11.6611 8.59629 11.5503L7.37452 10.2863C7.09598 9.99815 7.2527 9.51579 7.64743 9.44638L9.37883 9.1419C9.53058 9.11522 9.66154 9.02007 9.73381 8.88399L10.5584 7.33142Z" fill="white"/>
                                                <defs>
                                                    <linearGradient id="paint0_linear_2803_80113" x1="4.125" y1="-0.25" x2="19.125" y2="22.25" gradientUnits="userSpaceOnUse">
                                                        <stop stop-color="#FFCF54"/>
                                                        <stop offset="1" stop-color="#D69A01"/>
                                                    </linearGradient>
                                                </defs>
                                            </svg>
                                            @if(str_contains($doctor->image,"unsplash"))
                                                <img class="rounded-full h-14 w-14 object-cover" src="{{ $doctor->image }}" alt="Bali International Hospital">
                                            @else
                                                <img class="rounded-full h-14 w-14 object-cover" src="{{ asset_gcs($doctor->image) }}" alt="Bali International Hospital">
                                            @endif

                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <span class="text-gray-400 font-medium text-sm">{{ @$doctor->specialty->group_name_en }}</span>
                                            <span class="text-base font-semibold">{{ @$doctor->name }}</span>
                                            @if($doctor->experience_year)
                                                <span class="flex gap-1 text-sm items-center">
                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <g id="heroicons-micro/briefcase">
                                                        <g id="Union">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M11 4V3C11 1.89543 10.1046 1 9 1H7C5.89543 1 5 1.89543 5 3V4H4C2.89543 4 2 4.89543 2 6V9C2 10.1046 2.89543 11 4 11H12C13.1046 11 14 10.1046 14 9V6C14 4.89543 13.1046 4 12 4H11ZM9 2.5H7C6.72386 2.5 6.5 2.72386 6.5 3V4H9.5V3C9.5 2.72386 9.27614 2.5 9 2.5ZM9 9C9 9.55228 8.55228 10 8 10C7.44772 10 7 9.55228 7 9C7 8.44772 7.44772 8 8 8C8.55228 8 9 8.44772 9 9Z" fill="#667085"/>
                                                            <path d="M3 11.8291V11.9998C3 13.1044 3.89543 13.9998 5 13.9998H11C12.1046 13.9998 13 13.1044 13 11.9998V11.8291C12.6872 11.9397 12.3506 11.9998 12 11.9998H4C3.64936 11.9998 3.31278 11.9397 3 11.8291Z" fill="#667085"/>
                                                        </g>
                                                    </g>
                                                </svg>
                                                <span>{{ @$doctor->experience_year }} years</span>
                                            </span>
                                            @endif
                                        </div>
                                    </div>
                                    <hr>
                                    <a href="{{route('doctors.show',['uuid' => $doctor->uuid, 'name' => Str::slug($doctor->name)])}}" class="bg-[#0D4D8B] rounded-lg py-3 px-6 text-center hover:cursor-pointer">
                                        <span class="text-white font-semibold text-base">View Doctor</span>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @endif

                    {{-- related article--}}
                    @if($related_articles->count() > 0)
                        <div class="flex flex-col gap-4">
                            <span class="text-xl font-semibold">Related Articles</span>
                            @foreach($related_articles as $article)

                                <a href="{{ route('articles.show', $article->slug) }}" class="flex flex-row gap-6 hover:cursor-pointer">
                                    <div class="h-[90px] w-1/3">
                                        <img src="{{ asset_gcs($article->image) }}" alt=""
                                             class="rounded-lg object-cover h-full w-full">
                                    </div>
                                    <div class="h-[90px] w-3/4 flex justify-between flex-col gap-3">
                                        <div class="text-lg line-clamp-2 font-medium">{{ $article->title }}</div>
                                        <div class="flex text-sm text-[#667085] items-center gap-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"
                                                 fill="none">
                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                      d="M10 0.25C4.61522 0.25 0.25 4.61522 0.25 10C0.25 15.3848 4.61522 19.75 10 19.75C15.3848 19.75 19.75 15.3848 19.75 10C19.75 4.61522 15.3848 0.25 10 0.25ZM10.75 4C10.75 3.58579 10.4142 3.25 10 3.25C9.58579 3.25 9.25 3.58579 9.25 4V10C9.25 10.4142 9.58579 10.75 10 10.75H14.5C14.9142 10.75 15.25 10.4142 15.25 10C15.25 9.58579 14.9142 9.25 14.5 9.25H10.75V4Z"
                                                      fill="#667085" />
                                            </svg>
                                            <span>{{ $article->time_to_read == 0 ? 5 : $article->time_to_read}} min read</span>
                                            <div> • </div>
                                            <span>{{ \Carbon\Carbon::parse($article->created_at)->format('d M Y') }}</span>
                                        </div>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

@endsection

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.js"></script>
    <script>
        var toolbarOptions = [
            [{ 'header': [1, 2, false] }],
            ['bold', 'italic', 'underline'],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            ['link']
        ];

        const container = document.getElementById('editor');
        const quill = new Quill(container,{
            modules: {
                syntax: false,
                toolbar: null,
            },
            placeholder: 'Compose an epic...',
            theme: 'snow',
            readOnly: true,
            border: 0
        });
    </script>
@endpush

