<?php


use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;






// Auth::routes();

Route::prefix('cms')->group(function () {

    Route::get('/', [App\Http\Controllers\Cms\DashboardController::class, 'index'])->name('cms.dashboard')->middleware('auth');

    Route::get('/dashboard', [App\Http\Controllers\Cms\DashboardController::class, 'index'])->name('cms.dashboard');

    Route::get('/login', [App\Http\Controllers\Auth\LoginController::class, 'showLoginForm'])->name('cms.login');

    Route::post('/login', [App\Http\Controllers\Auth\LoginController::class, 'login'])->name('cms.login');

    Route::post('/logout', [App\Http\Controllers\Auth\LoginController::class, 'logout'])->name('cms.logout');


    // Route::group(['middleware' => 'auth'], function () {


        Route::prefix('doctor-profiles')->group(function () {
            Route::get('/', [App\Http\Controllers\Cms\DoctorProfileController::class, 'index'])->name('cms.doctor-profiles');
            Route::get('/edit/{id}', [App\Http\Controllers\Cms\DoctorProfileController::class, 'edit'])->name('cms.doctor-profiles.edit');
            Route::put('/update/{id}', [App\Http\Controllers\Cms\DoctorProfileController::class, 'update'])->name('cms.doctor-profiles.update');
        });


    Route::prefix('announcements')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\AnnouncementController::class, 'index'])->name('cms.announcements');
        Route::post('/store', [App\Http\Controllers\Cms\AnnouncementController::class, 'store'])->name('cms.announcements.store');
    });

    Route::prefix('careers')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\CareerController::class, 'index'])->name('cms.careers');
        Route::get('/create', [App\Http\Controllers\Cms\CareerController::class, 'create'])->name('cms.careers.create');
        Route::post('/store', [App\Http\Controllers\Cms\CareerController::class, 'store'])->name('cms.careers.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\CareerController::class, 'edit'])->name('cms.careers.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\CareerController::class, 'update'])->name('cms.careers.update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\CareerController::class, 'destroy'])->name('cms.careers.destroy');
        Route::get('/get-career-departments', [App\Http\Controllers\Cms\CareerController::class, 'getCareerDepartments'])->name('cms.careers.get-career-departments');
    });

    Route::prefix('careers')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\CareerController::class, 'index'])->name('cms.careers');
        Route::get('/create', [App\Http\Controllers\Cms\CareerController::class, 'create'])->name('cms.careers.create');
        Route::post('/store', [App\Http\Controllers\Cms\CareerController::class, 'store'])->name('cms.careers.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\CareerController::class, 'edit'])->name('cms.careers.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\CareerController::class, 'update'])->name('cms.careers.update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\CareerController::class, 'destroy'])->name('cms.careers.destroy');
        Route::get('/get-career-departments', [App\Http\Controllers\Cms\CareerController::class, 'getCareerDepartments'])->name('cms.careers.get-career-departments');
    });

    Route::prefix('trainings')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\TrainingController::class, 'index'])->name('cms.trainings.index');
        Route::get('/create', [App\Http\Controllers\Cms\TrainingController::class, 'create'])->name('cms.trainings.create');
        Route::post('/store', [App\Http\Controllers\Cms\TrainingController::class, 'store'])->name('cms.trainings.store');
        Route::get('/{id}/edit', [App\Http\Controllers\Cms\TrainingController::class, 'edit'])->name('cms.trainings.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\TrainingController::class, 'update'])->name('cms.trainings.update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\TrainingController::class, 'destroy'])->name('cms.trainings.destroy');
    });

    Route::prefix('promo-codes')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\PromoCodeController::class, 'index'])->name('cms.promo-codes');
        Route::get('/create', [App\Http\Controllers\Cms\PromoCodeController::class, 'create'])->name('cms.promo-codes.create');
        Route::post('/store', [App\Http\Controllers\Cms\PromoCodeController::class, 'store'])->name('cms.promo-codes.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\PromoCodeController::class, 'edit'])->name('cms.promo-codes.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\PromoCodeController::class, 'update'])->name('cms.promo-codes.update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\PromoCodeController::class, 'destroy'])->name('cms.promo-codes.destroy');
    });

    Route::prefix('packages')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\PackageController::class, 'index'])->name('cms.packages');
        Route::get('/create', [App\Http\Controllers\Cms\PackageController::class, 'create'])->name('cms.packages.create');
        Route::post('/store', [App\Http\Controllers\Cms\PackageController::class, 'store'])->name('cms.packages.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\PackageController::class, 'edit'])->name('cms.packages.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\PackageController::class, 'update'])->name('cms.packages.update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\PackageController::class, 'destroy'])->name('cms.packages.destroy');
        Route::get('/get-package-categories', [App\Http\Controllers\Cms\PackageController::class, 'getPackageCategories'])->name('cms.packages.get-package-categories');
    });


    Route::prefix('package-categories')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\PackageCategoryController::class, 'index'])->name('cms.package-categories');
        // Route::get('/create', [App\Http\Controllers\Cms\PackageCategoryController::class, 'create'])->name('cms.package-categories.create');
        // Route::post('/store', [App\Http\Controllers\Cms\PackageCategoryController::class, 'store'])->name('cms.package-categories.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\PackageCategoryController::class, 'edit'])->name('cms.package-categories.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\PackageCategoryController::class, 'update'])->name('cms.package-categories.update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\PackageCategoryController::class, 'destroy'])->name('cms.package-categories.destroy');
    });



    Route::prefix('package-types')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\PackageTypeController::class, 'index'])->name('cms.package-types');
        // Route::get('/create', [App\Http\Controllers\Cms\PackageTypeController::class, 'create'])->name('cms.package-types.create');
        // Route::post('/store', [App\Http\Controllers\Cms\PackageTypeController::class, 'store'])->name('cms.package-types.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\PackageTypeController::class, 'edit'])->name('cms.package-types.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\PackageTypeController::class, 'update'])->name('cms.package-types.update');
        // Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\PackageTypeController::class, 'destroy'])->name('cms.package-types.destroy');
    });

    Route::prefix('article-categories')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\ArticleCategoryController::class, 'index'])->name('cms.article-categories');
        Route::get('/create', [App\Http\Controllers\Cms\ArticleCategoryController::class, 'create'])->name('cms.article-categories.create');
        Route::post('/store', [App\Http\Controllers\Cms\ArticleCategoryController::class, 'store'])->name('cms.article-categories.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\ArticleCategoryController::class, 'edit'])->name('cms.article-categories.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\ArticleCategoryController::class, 'update'])->name('cms.article-categories.update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\ArticleCategoryController::class, 'destroy'])->name('cms.article-categories.destroy');
    });



    Route::prefix('articles')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\ArticleController::class, 'index'])->name('cms.articles');
        Route::get('/create', [App\Http\Controllers\Cms\ArticleController::class, 'create'])->name('cms.articles.create');
        Route::post('/store', [App\Http\Controllers\Cms\ArticleController::class, 'store'])->name('cms.articles.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\ArticleController::class, 'edit'])->name('cms.articles.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\ArticleController::class, 'update'])->name('cms.articles.update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\ArticleController::class, 'destroy'])->name('cms.articles.destroy');
    });

    Route::prefix('promos/')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\PromoController::class, 'index'])->name('cms.promos');
        Route::get('/create', [App\Http\Controllers\Cms\PromoController::class, 'create'])->name('cms.promos.create');
        Route::post('/store', [App\Http\Controllers\Cms\PromoController::class, 'store'])->name('cms.promos.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\PromoController::class, 'edit'])->name('cms.promos.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\PromoController::class, 'update'])->name('cms.promos.update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\PromoController::class, 'destroy'])->name('cms.promos.destroy');
    });

    Route::prefix('roles')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\RoleController::class, 'index'])->name('cms.roles');
        Route::get('/create', [App\Http\Controllers\Cms\RoleController::class, 'create'])->name('cms.roles.create');
        Route::post('/store', [App\Http\Controllers\Cms\RoleController::class, 'store'])->name('cms.roles.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\RoleController::class, 'edit'])->name('cms.roles.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\RoleController::class, 'update'])->name('cms.roles.update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\RoleController::class, 'destroy'])->name('cms.roles.destroy');
    });

    Route::prefix('users')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\UserController::class, 'index'])->name('cms.users');
        Route::get('/create', [App\Http\Controllers\Cms\UserController::class, 'create'])->name('cms.users.create');
        Route::post('/store', [App\Http\Controllers\Cms\UserController::class, 'store'])->name('cms.users.store');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\UserController::class, 'edit'])->name('cms.users.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\UserController::class, 'update'])->name('cms.users.update');
        Route::delete('/destroy/{id}', [App\Http\Controllers\Cms\UserController::class, 'destroy'])->name('cms.users.destroy');
        Route::post('/lock/{id}', [App\Http\Controllers\Cms\UserController::class, 'lock'])->name('cms.users.lock');

        Route::get('/my-profile', [App\Http\Controllers\Cms\UserController::class, 'profile'])->name('cms.users.profile');
        Route::put('/update-profile/{id}', [App\Http\Controllers\Cms\UserController::class, 'updateProfile'])->name('cms.users.update-profile');
    });

    Route::prefix('public-users')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\PublicUserController::class, 'index'])->name('cms.public-users');
        Route::get('/edit/{id}', [App\Http\Controllers\Cms\PublicUserController::class, 'edit'])->name('cms.public-users.edit');
        Route::put('/update/{id}', [App\Http\Controllers\Cms\PublicUserController::class, 'update'])->name('cms.public-users.update');
    });

    Route::prefix('reports')->group(function () {
        Route::get('/users', [App\Http\Controllers\Cms\ReportController::class, 'users'])->name('cms.report-users');
        Route::get('/appointments', [App\Http\Controllers\Cms\ReportController::class, 'appointments'])->name('cms.report-appointments');
        Route::get('/packages', [App\Http\Controllers\Cms\ReportController::class, 'packages'])->name('cms.report-packages');
        Route::post('/generates/appointment-teleconsultation', \App\Http\Controllers\Cms\Report\GenerateAppointmentTeleconsultationController::class)->name('cms.report.generates.appointment-teleconsultation');
        Route::post('/generates/appointment-hospital-visit', \App\Http\Controllers\Cms\Report\GenerateAppointmentHospitalVisitController::class)->name('cms.report.generates.hospital-visit');
        Route::post('/generates/public-user', \App\Http\Controllers\Cms\Report\GeneratePublicUserController::class)->name('cms.report.generates.public_user');
        Route::post('/generates/medical-package', \App\Http\Controllers\Cms\Report\GenerateMedicalPackageController::class)->name('cms.report.generates.medical-package');
        Route::get('/stop-report/{id}', [App\Http\Controllers\Cms\ReportController::class, 'stopReport'])->name('cms.report.stop-report');
        // Route::get('/get-report', [App\Http\Controllers\Cms\ReportController::class, 'getReport'])->name('cms.reports.get-report');
    });

    Route::prefix('bookings')->group(function () {
        Route::get('/patients', [App\Http\Controllers\Cms\Bookings\PatientController::class, 'index'])->name('cms.bookings.patients');
        Route::post('/patients/upload', [App\Http\Controllers\Cms\Bookings\PatientController::class, 'upload'])->name('cms.bookings.patients-upload');
        Route::get('/appointments', [App\Http\Controllers\Cms\Bookings\AppointmentController::class, 'index'])->name('cms.bookings.appointments');
        Route::get('/appointments/book/{uuid}/{doctor_uuid}/{type}', [App\Http\Controllers\Cms\Bookings\AppointmentController::class, 'book'])->name('cms.bookings.appointments.book');
        Route::get('/appointments/doctors', [\App\Http\Controllers\Cms\Bookings\AppointmentController::class, 'doctors'])->name('cms.bookings.appointments.doctors.index');
        Route::get('/appointments/doctors/{uuid}/{name}', [\App\Http\Controllers\Cms\Bookings\AppointmentController::class, 'showDoctor'])->name('cms.bookings.appointments.doctors.show')->middleware('count.view');
        Route::get('/medical-packages', [App\Http\Controllers\Cms\Bookings\MedicalPackageController::class, 'index'])->name('cms.bookings.medical-packages');
        Route::get('/medical-packages/type', [App\Http\Controllers\Cms\Bookings\MedicalPackageController::class, 'type'])->name('cms.bookings.medical-packages.type');
        Route::get('/medical-packages/category/{slug_category}', [App\Http\Controllers\Cms\Bookings\MedicalPackageController::class, 'category'])->name('cms.bookings.medical-packages.category');
        Route::get('/medical-packages/checkout/{uuid}', [\App\Http\Controllers\Cms\Bookings\MedicalPackageController::class, 'checkout'])->name('cms.bookings.medical_packages.checkout');
        Route::get('/send-mcu-report', [App\Http\Controllers\Cms\Bookings\SendMcuReportController::class, 'index'])->name('cms.bookings.send-mcu-report');
        Route::post('/send-mcu-report/upload', [App\Http\Controllers\Cms\Bookings\SendMcuReportController::class, 'upload'])->name('cms.bookings.send-mcu-report.upload');
        Route::get('/send-patient-invoice', [App\Http\Controllers\Cms\Bookings\SendPatientInvoiceController::class, 'index'])->name('cms.bookings.send-patient-invoice');
    });

    Route::prefix('general-payments')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\GeneralPaymentController::class, 'index'])->name('cms.general-payments');
        Route::get('/create', [App\Http\Controllers\Cms\GeneralPaymentController::class, 'create'])->name('cms.general-payments.create');
    });
    Route::prefix('inquiry')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\InquiryController::class, 'index'])->name('cms.inquiry');
        Route::get('/edit/{uuid}', [App\Http\Controllers\Cms\InquiryController::class, 'edit'])->name('cms.inquiry.edit');
        Route::patch('/update/{id}', [App\Http\Controllers\Cms\InquiryController::class, 'update'])->name('cms.inquiry.update');
    });
    Route::prefix('public-holiday')->group(function () {
        Route::get('/', [App\Http\Controllers\Cms\PublicHolidayController::class, 'index'])->name('cms.public-holiday');
        Route::get('/create', [App\Http\Controllers\Cms\PublicHolidayController::class, 'create'])->name('cms.public-holiday.create');
        Route::post('/store', [App\Http\Controllers\Cms\PublicHolidayController::class, 'store'])->name('cms.public-holiday.store');
        Route::get('/edit/{uuid}', [App\Http\Controllers\Cms\PublicHolidayController::class, 'edit'])->name('cms.public-holiday.edit');
        Route::patch('/update/{id}', [App\Http\Controllers\Cms\PublicHolidayController::class, 'update'])->name('cms.public-holiday.update');
        Route::delete('/delete/{uuid}', [App\Http\Controllers\Cms\PublicHolidayController::class, 'destroy'])->name('cms.public-holiday.destroy');
    });

});
