<div>
    <div class="px-6 py-5">
        <form wire:submit.prevent="save" enctype="multipart/form-data" id="excelUploadForm">
          @csrf
          <livewire:operator-bookings.send-mcu-report.form-patient wire:key="$wireKey" :wire_key="$wireKey" />
          @error('patient') <span class="error">{{ $message }}</span> @enderror
          <div class="space-y-6 mt-4">
            <!-- Drag & Drop Area -->
            <div id="dropArea" class="group relative flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-white px-6 py-10 text-center transition-colors hover:border-blue-400 hover:bg-blue-50">
              <div class="text-center">
                <svg class="mx-auto h-10 w-10 text-gray-400 group-hover:text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <span class="mt-2 block text-sm font-medium text-gray-900">
                  <span id="file-name" class="text-blue-600">Click to upload</span> or drag and drop
                </span>
                <span class="mt-1 block text-xs text-gray-500">
                  PDF File
                </span>
              </div>
              <input type="file" 
                     wire:model="file"
                     class="absolute inset-0 h-full w-full cursor-pointer opacity-0" 
                     name="file" 
                     id="excel_file" 
                     accept=".pdf">
            </div>
            @error('file') <span class="error">{{ $message }}</span> @enderror

            <!-- Status message -->
            <div id="uploadStatus"></div>
          </div>

          <!-- Footer -->
          <div class="flex items-center justify-end mt-6">
            <div wire:loading.remove wire:target="save" class="gap-x-4 border-t border-gray-100 px-6 py-4 w-100 text-right" id="form-submission">
              {{-- <button type="button" class="rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50" onClick="closeModalMassUpload()">
                Cancel
              </button> --}}
              <button type="submit" wire:loading.attr="disabled" wire:target="file" id="uploadButton" class="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 disabled:opacity-50">
                Upload File
              </button>
            </div>
          </div>
          {{-- <div wire:ignore> --}}
                <div  id="form-spinner">
                  <div wire:loading wire:target="file" class="w-full border border-brand p-4 text-center text-white rounded-xl pointer flex justify-center hover:cursor-not-allowed"
                      id="loading-component">
                      @include('landing-page.component.spinner_with_text')
                  </div>
                </div>
                <div wire:loading wire:target="save" id="form-spinner">
                  <div class="w-full border border-brand p-4 text-center text-white rounded-xl pointer flex justify-center hover:cursor-not-allowed"
                      id="loading-component">
                      @include('landing-page.component.spinner_with_text')
                  </div>
                </div>
            {{-- </div> --}}
        </form>
    </div>
</div>

@section('js')
    <script>
       
        const dropArea = document.getElementById('dropArea');
        const fileInput = document.getElementById('excel_file');
        const fileName = document.getElementById('file-name');
        const uploadProgress = document.getElementById('uploadProgress');
        const progressBar = document.getElementById('progress-bar');
        const progressPercent = document.getElementById('progress-percent');
        const uploadStatus = document.getElementById('uploadStatus');
        const uploadForm = document.getElementById('excelUploadForm');

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        // Handle dropped files
        dropArea.addEventListener('drop', handleDrop, false);

        // Handle file selection via click
        fileInput.addEventListener('change', handleFiles);

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight() {
            dropArea.classList.add('border-blue-400', 'bg-blue-50');
        }

        function unhighlight() {
            dropArea.classList.remove('border-blue-400', 'bg-blue-50');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            fileInput.files = files;
            handleFiles();
        }

        function handleFiles() {
            const files = fileInput.files;
            if (files.length) {
            fileName.textContent = files[0].name;
            }
        }

    </script>
@endsection