<?php
namespace App\Livewire\OperatorBookings\SendPatientInvoice;

use App\Enums\General\GeneralString;
use App\Models\AppointmentPatientSummary;
use App\Models\Company;
use App\Models\Insurance;
use App\Models\LandingPage\PublicUser;
use App\Models\Patient;
use App\Models\PatientAddress;
use App\Repositories\Patient\PatientRepository;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;

class FormPatient extends Component
{
    use WithPagination;

    public $isSelectedPatient = false;
    public $isOpenModalPatient;
    public $user;
    public $selectedPatient;
    public $allPatientIds;
    public $wireKey;
    public $perPage = 10;
    public $page = 1;
    public $search;
    public $patients = [];

    public function mount($wireKey)
    {
        $this->user             = PublicUser::find(Session::get('user_id'));
        $this->wireKey          = $wireKey;
    }

    protected $listeners = ['refreshPatients' => '$refresh'];

    public function updatedSearch()
    {
        // Reset the list and fetch the first page of results
        $this->resetPage();
        $this->page = 0;
        $this->patients = [];
        $this->loadMore(); // Fetch the first page
    }

    #[On('loadMore')]
    public function loadMore()
    {
        $this->page++;
        $newPatients = Patient::query()
        ->with('patientAddresses.masterDataProvince', 'phoneCountryCodes', 'whatsappCountryCodes', 'caregiverContactCountryCodes')
            ->when($this->search, function ($query) {
                $query->where('fullname', 'like', '%' . $this->search . '%')
                      ->orWhere('mr_no', 'like', '%' . $this->search . '%');
            })
            ->orderBy('id', 'desc')
            ->paginate($this->perPage, ['*'], 'page', $this->page);

        // Append the new patients to the existing list
        $this->patients = array_merge($this->patients, $newPatients->items());
        $this->dispatch('loadFinished');
    }

    public function render()
    {

        if (empty($this->patients)) {
            $this->patients = Patient::query()
                ->with('patientAddresses.masterDataProvince', 'phoneCountryCodes', 'whatsappCountryCodes', 'caregiverContactCountryCodes')
                ->when($this->search, function ($query) {
                    $query->where('fullname', 'like', '%' . $this->search . '%')
                        ->orWhere('mr_no', 'like', '%' . $this->search . '%');
                })
                ->orderBy('id', 'desc')
                ->paginate($this->perPage, ['*'], 'page', $this->page)
                ->items();
        }

        $this->allPatientIds    = collect($this->patients)->pluck('id')->toArray();
        return view('livewire.operator-bookings.send-patient-invoice.form-patient', [
            'patients' => $this->patients,
        ]);
    }

    public function onHandleSelectProfile()
    {
        $this->isOpenModalPatient = true;
    }

    public function onHandleCloseModal()
    {
        $this->isOpenModalPatient = false;
    }

    public function onHandleSelectedPatient($id): void
    {

        $this->isSelectedPatient    = true;
        $this->selectedPatient      = Patient::find($id);

        $patient_is_my_self = false;

        $this->dispatch('medical-package-updated-form-data',
            [
                'selected_patient_id'       => $id,
                'selected_patient_simrs_id' => $this->selectedPatient->simrs_patient_id,
                'selected_patient_public_user_id' => $this->selectedPatient->public_user_id,
                'selected_patient_email'    => $this->selectedPatient->email,
                'is_blade'                  => true,
                'patient_is_my_self'        => $patient_is_my_self,
                'wire_key'                  => $this->wireKey
        ],wireKey:$this->wireKey);

        $this->dispatch('medical-package-patient-selected',
            isSelectedPatient: $this->isSelectedPatient,
            id: $id,
            allPatientIds: $this->allPatientIds,
            wireKey: $this->wireKey);

        $this->skipRender();
    }

    public function continue()
    {
        $this->isOpenModalPatient = false;
        $this->render();
    }

    #[On('medical_package_error_validation')]
    public function onListenErrorValidation()
    {
        if(!$this->selectedPatient){
            session()->flash('error_selected_patient', 'Please choose a patient');
        }
    }


}
