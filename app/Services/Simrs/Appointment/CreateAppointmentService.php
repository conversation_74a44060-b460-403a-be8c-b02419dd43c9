<?php

namespace App\Services\Simrs\Appointment;

use App\Base\ServiceBase;
use App\Enums\Simrs\ErrorCode;
use App\Enums\Table\TransactionLog\TransactionName;
use App\Repositories\Log\TransactionLogData;
use App\Repositories\Simrs\Models\CreateAppointment;
use App\Repositories\Simrs\SimrsRepository;
use App\Responses\ServiceResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class CreateAppointmentService extends ServiceBase
{

    protected SimrsRepository $simrsRepository;

    public function __construct(protected array $data, protected ?Object $object = null)
    {
        $transactionLogData = new TransactionLogData();
        if($this->object){
            $transactionLogData->transactionlogable_type    = get_class($this->object);
            $transactionLogData->transactionlogable_id      = $this->object->id;
            $transactionLogData->trx_name                   = TransactionName::APPOINTMENT_CREATE;
        }
        $this->simrsRepository          = new SimrsRepository($transactionLogData);
    }

    /**
     * Validate the data
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validate(): \Illuminate\Contracts\Validation\Validator {
        return Validator::make($this->data, [
            'slot_id'     => 'required|string|max:50',
            'patient_id'  => 'required|string|max:50',
            'notes'       => 'nullable|string|max:255',
            'payor_code'  => 'nullable|max:50',
            'plan_code'   => 'nullable|max:50',
            'service_id'  => 'required|string|max:50',
        ]);
    }

    /**
     * main method of this service
     *
     * @return ServiceResponse
     */
    public function call(): ServiceResponse {

        // validate the request data
        if ($this->validate()->fails()) {
            Log::error(self::class . __FUNCTION__, [
                'ERROR VALIDATION ' => implode(',',$this->validate()->errors()->all()),
            ]);
            return self::error($this->validate()->errors()->getMessages(), implode(',',$this->validate()->errors()->all()),422);
        }

        try{

            $data               = new CreateAppointment();
            $data->slot_id      = $this->data['slot_id'];
            $data->patient_id   = $this->data['patient_id'];
            $data->notes        = @$this->data['notes'];
            $data->payor_code   = @(string)$this->data['payor_code'];
            $data->plan_code    = @(string)$this->data['plan_code'];
            $data->service_id   = $this->data['service_id'];

            $appointment        = $this->simrsRepository->createAppointment($data);
            if($appointment->status() != 200){

                if(str_contains(@$appointment->data()['error_body']['Error'], 'No Available slot')){
                    return self::error(null, 'No Available slot', 400, ErrorCode::NO_AVAILABLE_SLOT);
                }elseif(str_contains(@$appointment->data()['error_body']['Error'], 'already booked')){
                    return self::error(null, 'Patient already booked', 400, ErrorCode::PATIENT_ALREADY_BOOK);
                }

                throw new \Exception($appointment->message());
            }

            return self::success($appointment->data(), 'success');

        }catch (\Throwable $th) {

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            return self::error(null, $th->getMessage());

        }

    }
}
