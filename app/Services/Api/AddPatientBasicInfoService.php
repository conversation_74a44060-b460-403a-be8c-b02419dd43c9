<?php

namespace App\Services\Api;

use App\Base\ServiceBase;
use App\Enums\Table\Patient\Gender;
use App\Enums\Table\Patient\PercentageProgress;
use App\Enums\Table\Patient\RelationPatient;
use App\Enums\Table\RegisterUser\Origin;
use App\Repositories\CountryCode\CountryCodeRepository;
use App\Repositories\Patient\PatientRepository;
use App\Repositories\LandingPage\PublicUser\PublicUserRepository;
use App\Responses\ServiceResponse;
use App\Traits\General;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\Patient;
use App\Services\GCS\GoogleCloudService;
use App\Services\LandingPage\Patient\SyncToSimrsService;

class AddPatientBasicInfoService extends ServiceBase
{
    use General;

    protected PatientRepository $patientRepository;
    protected CountryCodeRepository $countryCodeRepository;
    protected GetListPatientService $getListPatientService;
    protected PublicUserRepository $publicUserRepository;

    public function __construct(protected array $data)
    {
        $this->patientRepository = new PatientRepository();
        $this->countryCodeRepository = new CountryCodeRepository();
        $this->getListPatientService = new GetListPatientService($this->data);
        $this->publicUserRepository     = new PublicUserRepository();
    }

    /**
     * Validate the data
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validate(): \Illuminate\Contracts\Validation\Validator
    {
        return Validator::make($this->data, [
            'first_name'        => 'required|string',
            'last_name'         => 'nullable|string',
            'is_only_first_name'=> 'required|boolean',
            'phone_number'      => 'required|string|max:15',
            'country_code'      => 'required|string|max:2',
            'gender'            => 'required|string|max:2',
            'dob'               => 'required|string|max:10',
            'id_number'         => 'required_if:origin,1',
            'passport_number'   => 'required_if:origin,2',
            'email'             => 'required|email',
            'origin'            => 'required|numeric'
        ], [
            'first_name.required' => 'First Name is required',
            // 'last_name.required' => 'Last Name is required',
            'is_only_first_name.required' => 'is_only_first_name is required',
            'phone_number.required' => 'Phone Number is required',
            'email.required' => 'Email is required',
            'gender.required' => 'Gender is required',
            'dob.required' => 'Date Of Birth is required',
            'country_code.required' => 'Country Code is required',
            'origin.required' => 'Origin is required',
            'id_number.required_if' => 'ID Number is required',
            'passport_number.required_if' => 'Passport Number is required',
        ]);
    }

    /**
     * Mapping requests into table fields
     *
     * @param array $data
     * @param object $selfPatient
     * @param object $countryCode
     * @return array
     */
    protected function mapRequest(array $data, object $selfPatient, object $countryCode): array
    {
        return [
            'mr_no'                     => 0,
            'parent_id'                 => $selfPatient->id,
            'ktp_number'                => !empty($data['id_number']) && $data['origin'] == Origin::DOMESTIC ? $data['id_number'] : '',
            'kitas_number'              => !empty($data['id_number']) && $data['origin'] == Origin::INTERNATIONAL ? $data['id_number'] : '',
            'passport_number'           => $data['passport_number'],
            'first_name'                => $data['first_name'],
            'last_name'                 => @$data['last_name'] ?? $data['first_name'],
            'is_only_first_name'        => @$data['is_only_first_name'],
            'fullname'                  => get_full_name($data['first_name'], @$data['last_name']),
            'dob'                       => $data['dob'] ?? '',
            'gender'                    => $data['gender'] ?? '',
            'contact_no'                => $data['phone_number'],
            'contact_country_code_id'   => $countryCode->id ?? 0,
            'origin'                    => $data['origin'],
            'email'                     => $data['email'] ?? '',
            'relation_patient'          => $data['relation_id'],
            'percentage_progress'       => PercentageProgress::COMPLETE_BASIC_INFO,
            'image'                     => $data['image'],
            'created_at' => Carbon::now()->setTimezone(config('app.timezone')),
            'updated_at' => Carbon::now()->setTimezone(config('app.timezone'))
        ];
    }

    /**
     * Mapping records into array fields
     *
     * @param array $record
     * @return array
     */
    public function mapResponse(array $record): array
    {
        $relationPatient = RelationPatient::getLabel((string)$record['relation_patient']);
        $gcs = new GoogleCloudService();
        $patient = Patient::find($record['id']);
        return [
            "patient_id" => $record['id'],
            "first_name" => $record['first_name'],
            "last_name" => @$record['is_only_first_name'] ? null : @$record['last_name'],
            "is_only_first_name" => @$record['is_only_first_name'] ? true : false,
            "phone_number" => $record['contact_no'],
            "country_code" => $record['phone_country_codes'][0]['extension'],
            "image" => $record['image'] ? $gcs->getStaticUrl($record['image']) : null,
            "patient_relation" => [
                "relation_id" => (string)$record['relation_patient'],
                "relation_name" => $relationPatient != "0" ? $relationPatient : null
            ],
            "gender" => $record['gender'],
            "dob" => $record['dob'],
            "id_number" => !empty($record['ktp_number']) ? $record['ktp_number'] : $record['kitas_number'],
            "passport_number" => $record['passport_number'],
            "email" => $record['email'],
            "is_complete_profile" => $patient->isCompleteData,
            "is_complete_trakcare" => $patient->isMandatoryComplete,
            "origin" => !empty($record['ktp_number']) ? Origin::DOMESTIC : Origin::INTERNATIONAL
        ];
    }

    /**
     * main method of this service
     *
     * @return ServiceResponse
     */
    public function call(): ServiceResponse
    {

        // validate the request data
        if ($this->validate()->fails()) {
            return self::error($this->validate()->errors()->getMessages(), implode(',', $this->validate()->errors()->all()), 422);
        }

        try {
            DB::beginTransaction();

            $queryColumn = "";

            switch ($this->data['origin']) {
                case Origin::DOMESTIC:
                    $queryColumn = "ktp_number";
                    break;
                case Origin::INTERNATIONAL:
                    $queryColumn = "passport_number";
                    break;
            }

            $countryCode = $this->countryCodeRepository->getByExtension($this->data['country_code']);
            throw_if(!$countryCode, 'BIH_06');
            $selfPatient = $this->patientRepository->getSelfPatient($this->data['user_id']);
            throw_if(!$selfPatient, 'BIH_06');


            $gcs = new GoogleCloudService();

            $this->data['image'] = $gcs->getClearPath($this->data['image']);

            $data = $this->data;
            if (!$this->data['patient_id']) {

                if (!empty($this->data['id_number'])) {
                    $existPatient = $this->patientRepository->getByIdNumber($queryColumn, $this->data['id_number']);
                } else {
                    $existPatient = $this->patientRepository->getByIdNumber($queryColumn, $this->data['passport_number']);
                }
                throw_if($existPatient, 'BIH_10');

                $patient = $this->patientRepository->create($this->mapRequest($this->data, $selfPatient, $countryCode));
            } else {
                $patient = $this->patientRepository->getById($data['patient_id']);

                $patient->parent_id                 = $patient->id == $selfPatient->id ? null : $selfPatient->id;
                $patient->ktp_number                = !empty($data['id_number']) && $data['origin'] == Origin::DOMESTIC ? $data['id_number'] : '';
                $patient->kitas_number              = !empty($data['id_number']) && $data['origin'] == Origin::INTERNATIONAL ? $data['id_number'] : '';
                $patient->passport_number           = @$data['passport_number'];
                $patient->first_name                = @$data['first_name'];
                $patient->last_name                 = @$data['last_name'];
                $patient->fullname                  = get_full_name($data['first_name'], @$data['last_name']);
                $patient->dob                       = $data['dob'] ?? '';
                $patient->gender                    = $data['gender'] ?? 0;
                $patient->contact_no                = $data['phone_number'];
                $patient->contact_country_code_id   = $countryCode->id ?? 0;
                $patient->email                     = $data['email'] ?? '';
                $patient->relation_patient          = $data['relation_id'];
                $patient->percentage_progress       = PercentageProgress::COMPLETE_BASIC_INFO;
                $patient->image                     = $data['image'];
                $patient->origin                    = @$data['origin'];
                $patient->updated_at                = Carbon::now()->setTimezone(config('app.timezone'));

                $patient->save();
            }

            if($patient->id == $selfPatient->id){
                $publicUser = $this->publicUserRepository->getById($data['user_id']);

                $publicUser->first_name                = @$data['first_name'];
                $publicUser->last_name                 = @$data['last_name'];
                $publicUser->is_only_first_name        = @$data['is_only_first_name'];
                $publicUser->ktp_number                = @$data['id_number'];
                $publicUser->passport_number           = @$data['passport_number'];
                $publicUser->fullname                  = get_full_name($data['first_name'], @$data['last_name']);
                $publicUser->dob                       = @$data['dob'];
                $publicUser->gender                    = $data['gender'] ?? 0;
                $publicUser->phone                     = $data['phone_number'];
                $publicUser->country_code_id           = $countryCode->id ?? 0;
                $publicUser->email                     = @$data['email'];
                $publicUser->origin                    = @$data['origin'];
                $publicUser->updated_at                = Carbon::now()->setTimezone(config('app.timezone'));

                $publicUser->phone_verified_at         = @$data['phone_number'] ? now() : null;
                $publicUser->save();
            }

            $newPatient = $this->patientRepository->getById($patient->id);
            DB::commit();

            (new SyncToSimrsService($patient->id))->call();

            $response = $this->mapResponse($newPatient->toArray());

            return self::success($response, 'Patient basic info created successfully');
        } catch (\Throwable $th) {

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            $errorCode = $this->findMessageErrorCodeByCode($th->getMessage());
            if ($errorCode) {
                return self::error(null, $errorCode['message'], 400, $errorCode['code']);
            }

            return self::error(null, $th->getMessage());
        }
    }
}
