<div class="flex flex-col justify-between gap-4 h-full">
    <div class="flex flex-col gap-4">
        @if(@$packageCategory->image)
            <img class="rounded-2xl h-[185px] lg:h-[249px] object-cover" src="{{ asset_gcs($packageCategory->image) }}" alt="package category bithealth">
        @else
            <img class="rounded-2xl h-[185px] lg:h-[249px] object-cover" src="https://images.pexels.com/photos/806427/pexels-photo-806427.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="package category bithealth">
        @endif
        <span class="text-xl font-semibold line-clamp-2 linguise_package_category">{{ $packageCategory->name }}</span>
        <span class="text-base font-light text-[#6D7079] line-clamp-4">{{ $packageCategory->description }}</span>
    </div>
    <a href="{{route('medical_packages.category', ['slug_category'=>$packageCategory->slug])}}" class="w-full py-3 text-brand rounded-xl hover:cursor-pointer text-base font-medium flex gap-2 mt-auto">
        <span>See Packages</span>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.2636 18.0147C6.61508 18.3662 7.18492 18.3662 7.5364 18.0147L16.2 9.35111V16.1783C16.2 16.6754 16.6029 17.0783 17.1 17.0783C17.5971 17.0783 18 16.6754 18 16.1783V7.17832C18 6.68126 17.5971 6.27832 17.1 6.27832H8.1C7.60294 6.27832 7.2 6.68126 7.2 7.17832C7.2 7.67538 7.60294 8.07832 8.1 8.07832H14.9272L6.2636 16.7419C5.91213 17.0934 5.91213 17.6632 6.2636 18.0147Z" fill="#0D4D8B"/>
        </svg>
    </a>
</div>
