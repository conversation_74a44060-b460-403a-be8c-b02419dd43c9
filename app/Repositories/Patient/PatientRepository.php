<?php

namespace App\Repositories\Patient;

use App\Models\Patient;

class PatientRepository
{
    protected Patient $patient;

    public function __construct(){
        $this->patient = new Patient();
    }

    public function create(array $data)
    {
        return $this->patient->create($data);
    }

    public function update(array $data, int $id)
    {
        $patient = $this->patient->find($id);
        if ($patient) {
            $patient->update($data); // This will trigger the observer
        }
    }

    public function getByIdNumber(string $field, string $idNumber)
    {
        return $this->patient->with('phoneCountryCodes', 'whatsappCountryCodes', 'caregiverContactCountryCodes')->where($field, $idNumber)->first();
    }

    public function getById(int $id)
    {
        return $this->patient->with('phoneCountryCodes', 'whatsappCountryCodes', 'caregiverContactCountryCodes')->find($id);
    }

    public function getByUUID(string $uuid)
    {
        return $this->patient->with([
            'phoneCountryCodes', 'whatsappCountryCodes', 'caregiverContactCountryCodes',
            'patientAddresses', 'patientAddresses.masterDataCity',
        ])->where('uuid', $uuid)->first();
    }

    public function getSelfPatient(int $publicUserId)
    {
        return $this->patient->with('phoneCountryCodes', 'whatsappCountryCodes', 'caregiverContactCountryCodes', 'patientInvoiceSendLogs')->where('public_user_id', $publicUserId)->first();
    }

    public function getOtherPatients(int $id)
    {
        return $this->patient->with('phoneCountryCodes', 'whatsappCountryCodes', 'caregiverContactCountryCodes','patientInvoiceSendLogs')->where('parent_id', $id)->get();
    }

    public function getPatientsForOperator()
    {
        return $this->patient->with('phoneCountryCodes', 'whatsappCountryCodes', 'caregiverContactCountryCodes')->whereNotNull('public_user_id')->get();
    }

    public function destroyById(int $id)
    {
        $patient = $this->patient->find($id);
        return $patient->delete();
    }

    public function checkKTPNumber(int $id, string $ktpNumber)
    {
        return $this->patient->where('id','!=', $id)->where('ktp_number', $ktpNumber)->exists();
    }

}
