@section('title', 'Bali International Hospital')
@extends("landing-page.layouts.landingPageTemplate")

@section("content")
<div class="mx-4 md:mx-14 my-16 z-10">
    <div class="grid grid-cols-1 md:grid-cols-2">
        <div class="z-10 md:block hidden">
            <div class="w-60 h-[93px]">
                <img src="{{ asset('assets/svg/bih-logo.png') }}" alt="">
            </div>
            <div class="mt-[3.087rem] ml-4">
                <h1 class="text-30-38">Welcome to</h1>
                <h1 class="text-30-38">Bali International Hospital</h1>
                <p class="text-typo-grey">Caring for You, every step of the way.</p>
            </div>
        </div>

        {{-- This is section for create account diplay on registration page --}}
        <div id="pre-login" class="z-10 bg-white relative rounded-[24px]">
            <a href="/login" class="absolute right-6 top-6 hover:cursor-pointer">
                <img src="{{ asset('assets/svg/cross-blue.svg') }}" alt="">
            </a>
            <div id="create-account" class="z-10">
                <div class="flex flex-col -mx-4 md:mx-0 px-4 md:min-h-[45.75rem] md:w-599 justify-center gap-3 md:px-[4.469rem] md:py-[2rem] rounded-3xl bg-white shadow-md items-center md:items-start">
                    <div class="w-60 h-[93px] md:hidden">
                        <img src="{{ asset('assets/svg/bih-logo.png') }}" alt="">
                    </div>
                    <div class="mb-3 flex flex-col gap-2">
                        <span class="text-2xl md:text-3xl">Create Account</span>
                        <div class="flex text-sm md:text-base font-light">
                            <h1>Have Account?&nbsp;</h1>
                            <h1><a href="{{ route('login.public.index') }}" class="text-brand font-bold">Login here!</a></h1>
                        </div>
                    </div>
                    <!-- <div class="w-full md:w-456 md:h-14 px-2 py-2 bg-option-bg rounded-xl flex justify-center items-center mx-2">
                        <button class="w-full bg-brand md:w-56 md:h-10 rounded-lg text-white text-center py-2" id="domesticBtn">Domestic</button>
                        <button class="w-full md:w-56 md:h-10 rounded-lg text-brand text-center py-2" id="interBtn">International</button>
                    </div> -->

                    <div id="domestic" class="w-full">
                        <form action="{{ route('registration.store') }}" method="POST" id="domestic-form">
                            @csrf
                            <div class="mt-4">
                                <label for="firstName" class="text-16-24">First Name <span class="text-error-50">*</span></label>
                                <div class="py-3">
                                    <input type="text" name="firstName" id="firstName" value="{{ old('firstName') }}" placeholder="First Name"
                                        class="rounded-xl w-full h-14 px-6 border identity-number {{ $errors->has('firstName') ? 'border-typo-error' : 'border-border-input' }}">
                                </div>
                                @error('firstName')
                                <div class="text-typo-error">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class=" inline-flex items-center">
                                <label class="relative flex items-center p-3 rounded-full cursor-pointer">
                                    <input type="checkbox" id="toggleLastName" name="toggleLastName" value="false"
                                        class="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-gray-900 checked:bg-gray-900 checked:before:bg-gray-900 hover:before:opacity-10" />
                                    <span
                                        class="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor"
                                            stroke="currentColor" stroke-width="1">
                                            <path fill-rule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <label class="mt-px font-light text-gray-700 cursor-pointer select-none">
                                    I only have a First Name
                                </label>
                            </div>
                            <div class="mt-4" id="lastNameField">
                                <label for="lastName" class="text-16-24">Last Name <span class="text-error-50">*</span></label>
                                <div class="py-3">
                                    <input type="text" name="lastName" id="lastName" value="{{ old('lastName') }}" placeholder="Last Name" class="rounded-xl w-full h-14 px-6 border identity-number {{ $errors->has('lastName') ? 'border-typo-error' : 'border-border-input' }}">
                                </div>
                                @error('lastName')
                                <div class="text-typo-error">{{ $message }}</div>
                                @enderror
                            </div>
                            <!-- <div class="mt-4">
                                    <label for="identityNumber" class="text-16-24">Identity Card Number (NIK)
                                        <span class="text-error-50">*</span>
                                    </label>
                                    <input type="hidden" name="identityType" id="identityType" value="ktp">
                                    <input type="number" name="identityNumber" id="identityNumber"
                                           value="{{ old('identityNumber') }}"
                                           placeholder="Enter your identity card number"
                                           class="rounded-xl w-full h-14 px-6 border identity-number
           {{ $errors->has('identityNumber') ? 'border-typo-error' : 'border-border-input' }}">
                                    @error('identityNumber')
                                    <div class="text-typo-error">{{ $message }}</div>
                                    @enderror
                                    <div id="identityNumberError" class="text-typo-error" style="display:none;">NIK must be 16 digits</div>
                                </div> -->
                            <!-- <div class="mt-4 flex flex-col md:flex-row gap-2 md:gap-0"> -->
                            <!-- <div class="flex-1 md:mr-6">
                                    <label for="dateOfBirth" class="text-16-24">Date of Birth <span class="text-error-50">*</span></label>
                                    <div class="relative">
                                        <input type="text" id="dateOfBirth" name="dateOfBirth" class="py-3 pe-11 block rounded-xl w-full md:w-220 h-14 mt-2 px-6 border {{ $errors->has('dateOfBirth') ? 'border-typo-error' : 'border-border-input' }}" value="{{ old('dateOfBirth') }}" placeholder="dd/mm/yyyy">
                                        <div class="absolute inset-y-0 end-0 flex items-center pointer-events-none z-20 pe-4">
                                            {!! file_get_contents('assets/svg/calendar.svg') !!}
                                        </div>
                                    </div>
                                    <div class="text-typo-error dob-error hidden"></div>
                                    @error('dateOfBirth')
                                    <div class="text-typo-error">{{ $message }}</div>
                                    @enderror
                                </div> -->
                            <!-- <input type="text" name="sex" value="" hidden class="sex-value"> -->
                            <!-- <div class="flex flex-col w-full">
                                    <label for="sex">Sex <span class="text-error-50">*</span></label>
                                    <div class="hs-dropdown relative inline-flex mt-2 z-10">
                                        <button id="hs-dropdown-default" type="button"
                                            class="hs-dropdown-toggle rounded-xl flex flex-row justify-between
                                                items-center focus:border-blue-700
                                                focus:border-2 w-full h-14 px-6 border"
                                            aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                            <span class="text-gray-400 form-sex">Select Sex</span>
                                            <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m6 9 6 6 6-6" />
                                            </svg>
                                        </button>

                                        <div class="hs-dropdown-menu transition-[opacity,margin]
                                        duration hs-dropdown-open:opacity-100 opacity-0
                                        hidden min-w-60 bg-white shadow-md rounded-lg p-1
                                        space-y-0.5 mt-2 after:h-4 after:absolute
                                        after:-bottom-4 after:start-0 after:w-full
                                        before:h-4 before:absolute before:-top-4
                                        before:start-0 before:w-full"
                                            role="menu" aria-orientation="vertical"
                                            aria-labelledby="hs-dropdown-default">
                                            @foreach($genders as $gender)
                                            <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800
                                                hover:bg-gray-100 focus:outline-none focus:bg-gray-100" onclick="onClickGender('{{ $gender->key  }}','{{ $gender->value  }}')" role="menuitem">
                                                {{ $gender->key  }}
                                            </a>
                                            @endforeach
                                        </div>
                                    </div>
                                    @error('sex')
                                    <div class="text-typo-error">{{ $message }}</div>
                                    @enderror
                                </div> -->
                            <!-- </div> -->
                            <!-- <div class="mt-4">
                                    <label for="countryCodeId" class="text-16-24">Phone Number <span class="text-error-50">*</span></label>
                                    <div class="flex">
                                        <input type="hidden" name="countryCodeId" id="countryCodeId" value="{{ $id_country_code->id }}">
                                        <div class="hs-dropdown relative">
                                            <button id="hs-dropdown-with-icons" type="button" class="hs-dropdown-toggle py-3 px-2 md:px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-xl bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:pointer-events-none w-20 md:w-32 h-14 mt-2 mr-2 border border-border-input" disabled>
                                                <img id="selected-country-flag" class="selected-country-code" src="{{ asset_gcs('vendor/blade-flags/country-id.svg') }}" alt="" width="25" height="25"/> <span id="selected-country-code">+62</span>
                                                <svg class="flex-shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 hidden md:block" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m7 15 5 5 5-5"/><path d="m7 9 5-5 5 5"/></svg>
                                            </button>
                                            <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden w-32 bg-white shadow-md rounded-lg p-2 mt-2 after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4 before:absolute before:-top-4 before:start-0 before:w-full" aria-labelledby="hs-dropdown-default">
                                                <div class="py-2 first:pt-0 last:pb-0">
                                                    @foreach($country_codes as $record)
                                                        <a class="code-country flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm hover:bg-gray-100 focus:outline-none focus:bg-gray-100" href="#" data-code="{{ $record->id }}">
                                                            <img src="{{ asset_gcs('vendor/blade-flags/country-'.strtolower($record->country_code).'.svg') }}" alt="" width="25" height="25"/> <span>+{{ $record->extension }}</span>
                                                        </a>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                        <input type="text" name="phoneNumber" id="phoneNumber" value="{{ old('phoneNumber') }}"
                                               placeholder="Enter your phone number"
                                               class="phone-number rounded-xl w-456 h-14 mt-2 px-6 border {{ $errors->has('phoneNumber') ? 'border-typo-error' : 'border-border-input' }}" />
                                    </div>
                                    @error('phoneNumber')
                                    <div class="text-typo-error">{{ $message }}</div>
                                    @enderror
                                    <small id="phoneError" class="text-red-500" style="display: none;">Phone number must be between 7 and 13 characters.</small>
                                </div> -->
                            <div class="mt-4">
                                <label for="email" class="text-16-24">Email <span class="text-error-50">*</span></label>
                                <input type="email" name="email" id="email" value="{{ old('email') }}" placeholder="Enter your email" class="rounded-xl w-full h-14 px-6 border  {{ $errors->has('email') ? 'border-typo-error' : 'border-border-input' }}">
                                @error('email')
                                <div class="text-typo-error">{{ $message }}</div>
                                @enderror
                            </div>
                            <input type="hidden" name="recaptcha" id="recaptcha-domestic">
                            @if ($errors->has('recaptcha'))
                            <div class="mt-4">
                                <span class="text-typo-error">{{ $errors->first('recaptcha') }}</span>
                            </div>
                            @endif
                            <div class="mt-4 inline-flex items-center">
                                <label class="relative flex items-center p-3 rounded-full cursor-pointer">
                                    <input type="checkbox" name="isNewsletter"
                                        class="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-gray-900 checked:bg-gray-900 checked:before:bg-gray-900 hover:before:opacity-10" />
                                    <span
                                        class="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor"
                                            stroke="currentColor" stroke-width="1">
                                            <path fill-rule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <label class="mt-px font-light text-gray-700 cursor-pointer select-none">
                                    Subscribe to our newsletter
                                </label>
                            </div>
                            <div class="drop-shadow-md mt-4">
                                <button type="submit" class="bg-brand-light text-center rounded-xl w-full h-14 px-6 border text-white text-16"
                                    disabled id="submit-button">Create Account</button>
                            </div>
                            <div href="#" class="w-[28.5rem] h-[3.5rem] rounded-lg border px-4 items-center flex hidden" id="loading-button">
                                @include('landing-page.component.spinner_with_text', ['utility' => 'bg-transparent flex flex-row gap-1 items-center text-brand', 'text' => 'Creating account...'])
                            </div>
                        </form>
                    </div>

                    <div id="international" class="hidden w-full">
                        <form action="{{ route('registration.store') }}" method="POST" id="international-form">
                            @csrf
                            <div class="mt-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6">
                                    <div class="flex-1">
                                        <label for="firstName" class="text-16-24">First Name <span class="text-error-50">*</span></label>
                                        <div class="py-3">
                                            <input type="text" name="firstName" id="firstName" value="{{ old('firstName') }}" placeholder="First Name" class="rounded-xl w-full h-14 px-6 border identity-number {{ $errors->has('firstName') ? 'border-typo-error' : 'border-border-input' }}">
                                        </div>
                                        @error('firstName')
                                        <div class="text-typo-error">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="flex-1">
                                        <label for="lastName" class="text-16-24">Last Name <span class="text-error-50">*</span></label>
                                        <div class="py-3">
                                            <input type="text" name="lastName" id="lastName" value="{{ old('lastName') }}" placeholder="Last Name" class="rounded-xl w-full h-14 px-6 border identity-number {{ $errors->has('lastName') ? 'border-typo-error' : 'border-border-input' }}">
                                        </div>
                                        @error('lastName')
                                        <div class="text-typo-error">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4">
                                <label for="identityNumber">Passport Number <span class="text-error-50">*</span></label>
                                <input type="hidden" name="identityType" id="identityType" value="passport">
                                <input type="text" name="identityNumber" id="identityNumber" value="{{ old('identityNumber') }}" placeholder="Enter your passport number"
                                    class="rounded-xl w-full h-14 px-6 border identity-number {Number{ $errors->has('identityNumber') ? 'border-typo-error' : 'border-border-input' }}">
                                @error('identityNumber')
                                <div class="text-typo-error">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="mt-4">
                                <div class="flex flex-col md:flex-row gap-2 md:gap-0">
                                    <div class="flex-1 md:mr-6">
                                        <label for="dateOfBirth_international" class="text-16-24">Date of Birth <span class="text-error-50">*</span></label>
                                        <div class="relative">
                                            <input type="text" id="dateOfBirth_international" name="dateOfBirth" class="py-3 pe-11 block rounded-xl w-full md:w-220 h-14 mt-2 px-6 border {{ $errors->has('dateOfBirth') ? 'border-typo-error' : 'border-border-input' }}" value="{{ old('dateOfBirth') }}" placeholder="dd/mm/yyyy">
                                            <div class="absolute inset-y-0 end-0 flex items-center pointer-events-none z-20 pe-4">
                                                {!! file_get_contents('assets/svg/calendar.svg') !!}
                                            </div>
                                        </div>
                                        <div class="text-typo-error dob-error hidden"></div>
                                        @error('dateOfBirth')
                                        <div class="text-typo-error">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <input type="text" name="sex" value="" hidden class="sex-value">
                                    <div class="flex flex-col w-full">
                                        <label for="sex">Sex <span class="text-error-50">*</span></label>
                                        <div class="hs-dropdown relative inline-flex mt-2 z-20">
                                            <button id="hs-dropdown-default" type="button"
                                                class="hs-dropdown-toggle rounded-xl flex flex-row justify-between
                                                items-center focus:border-blue-700
                                                focus:border-2 w-full h-14 px-6 border"
                                                aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                                <span class="text-gray-400 form-sex">Select Sex</span>
                                                <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="m6 9 6 6 6-6" />
                                                </svg>
                                            </button>


                                            <div class="hs-dropdown-menu transition-[opacity,margin]
                                        duration hs-dropdown-open:opacity-100 opacity-0
                                        hidden min-w-60 bg-white shadow-md rounded-lg p-1
                                        space-y-0.5 mt-2 after:h-4 after:absolute
                                        after:-bottom-4 after:start-0 after:w-full
                                        before:h-4 before:absolute before:-top-4
                                        before:start-0 before:w-full"
                                                role="menu" aria-orientation="vertical"
                                                aria-labelledby="hs-dropdown-default">
                                                @foreach($genders as $gender)
                                                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800
                                                hover:bg-gray-100 focus:outline-none focus:bg-gray-100" onclick="onClickGender('{{ $gender->key  }}','{{ $gender->value  }}')" role="menuitem">
                                                    {{ $gender->key  }}
                                                </a>
                                                @endforeach
                                            </div>
                                        </div>
                                        @error('sex')
                                        <div class="text-typo-error">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4">
                                <label for="countryCodeId">Phone Number <span class="text-error-50">*</span></label>
                                <div class="flex">
                                    <input type="hidden" name="countryCodeId" id="countryCodeId" value="{{ $id_country_code->id }}">
                                    <div class="hs-dropdown relative z-10">
                                        <button id="hs-dropdown-with-icons" type="button" class="hs-dropdown-toggle py-3 px-2 md:px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-xl bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none w-20 md:w-32 h-14 mt-2 mr-2 border border-border-input">
                                            <img id="selected-country-flag" alt="" class="selected-country-code" src="{{ asset_gcs('vendor/blade-flags/country-id.svg') }}" width="25" height="25" /> <span id="selected-country-code">+62</span>
                                            <svg class="flex-shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 hidden md:block" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m7 15 5 5 5-5" />
                                                <path d="m7 9 5-5 5 5" />
                                            </svg>
                                        </button>
                                        <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden w-32 bg-white shadow-md rounded-lg p-2 mt-2 after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4 before:absolute before:-top-4 before:start-0 before:w-full" aria-labelledby="hs-dropdown-default">
                                            <div class="py-2 first:pt-0 last:pb-0 h-44 overflow-auto">
                                                <!-- Filter input -->
                                                <input type="text" id="filterInput" class="w-full p-2 mb-4 border border-gray-300 rounded-lg text-xs" placeholder="Name or code">

                                                <!-- Country codes list -->
                                                <div id="countryList">
                                                    @foreach($country_codes as $record)
                                                    <a class="code-country flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm hover:bg-gray-100 focus:outline-none focus:bg-gray-100" href="#"
                                                        data-code="{{ $record->id }}"
                                                        data-name="{{ $record->country_name }}"
                                                        data-extension="{{ $record->extension }}">
                                                        <img src="{{ asset_gcs('vendor/blade-flags/country-'.strtolower($record->country_code).'.svg') }}" alt="" width="25" height="25" /> <span>+{{ $record->extension }}</span>
                                                    </a>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="text" name="phoneNumber" id="phoneNumberInternational" value="{{ old('phoneNumber') }}" placeholder="Enter your phone number" class="phone-number rounded-xl w-456 h-14 mt-2 px-6 border  {{ $errors->has('phoneNumber') ? 'border-typo-error' : 'border-border-input' }}">
                                </div>
                                @error('phoneNumber')
                                <div class="text-typo-error">{{ $message }}</div>
                                @enderror
                                <small id="phoneErrorInternational" class="text-red-500" style="display: none;">Phone number must be between 7 and 13 characters.</small>
                            </div>
                            <div class="mt-4">
                                <label for="email">Email <span class="text-error-50">*</span></label>
                                <input type="email" name="email" id="email" value="{{ old('email') }}" placeholder="Enter your email"
                                    class="rounded-xl w-full h-14 px-6 border  {{ $errors->has('email') ? 'border-typo-error' : 'border-border-input' }}">
                                @error('email')
                                <div class="text-typo-error">{{ $message }}</div>
                                @enderror
                            </div>
                            <input type="hidden" name="recaptcha" id="recaptcha-international">
                            @if ($errors->has('recaptcha'))
                            <div class="mt-4">
                                <span class="text-typo-error">{{ $errors->first('recaptcha') }}</span>
                            </div>
                            @endif
                            <div class="mt-4 inline-flex items-center">
                                <label class="relative flex items-center p-3 rounded-full cursor-pointer">
                                    <input type="checkbox" name="isNewsletter"
                                        class="before:content[''] peer relative h-5 w-5 cursor-pointer appearance-none rounded-md border border-blue-gray-200 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-gray-500 before:opacity-0 before:transition-opacity checked:border-gray-900 checked:bg-gray-900 checked:before:bg-gray-900 hover:before:opacity-10" />
                                    <span
                                        class="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-2/4 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor"
                                            stroke="currentColor" stroke-width="1">
                                            <path fill-rule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </label>
                                <label class="mt-px font-light text-gray-700 cursor-pointer select-none">
                                    Subscribe to our newsletter
                                </label>
                            </div>
                            <div class="drop-shadow-md mt-4">
                                <button type="submit" class="submit-button bg-brand-light text-center rounded-xl w-full h-14 px-6 text-white text-16" disabled
                                    id="">Create Account</button>
                            </div>
                            <div href="#" class="w-[28.5rem] h-[3.5rem] rounded-lg border px-4 items-center flex hidden loading-button">
                                @include('landing-page.component.spinner_with_text', ['utility' => 'bg-transparent flex flex-row gap-1 items-center text-brand', 'text' => 'Creating account...'])
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    </div>

</div>
<div class="z-0 fixed bottom-0 left-0">
    <img src="{{ asset('assets/svg/gradient-green.svg') }}" alt="">
</div>
<div class="z-0 absolute top-0 right-0">
    <img src="{{ asset('assets/svg/gradient-blue.svg') }}" alt="">
</div>
<div id="inline-badge"></div>
@endsection
@push('script')

<script>
    const phoneInput = document.getElementById("phoneNumber");
    const phoneError = document.getElementById("phoneError");
    const checkbox = document.getElementById("toggleLastName");
    const lastNameField = document.getElementById("lastNameField");
    const lastNameInput = document.getElementById("lastName");
    const minLength = 7;
    const maxLength = 13;

    checkbox.addEventListener("change", function() {
        if (this.checked) {
            this.value = "true";
            lastNameInput.value = "";
            lastNameField.style.display = "none";
        } else {
            this.value = "false";
            lastNameField.style.display = "block";
        }
    });

    phoneInput.addEventListener("input", function() {
        // Limit the maximum length
        if (phoneInput.value.length > maxLength) {
            phoneInput.value = phoneInput.value.slice(0, maxLength);
        }

        // Validate the input length
        const isValid = phoneInput.value.length >= minLength && phoneInput.value.length <= maxLength;

        // Show or hide the error message
        phoneError.style.display = isValid ? "none" : "block";

        // Add or remove error border
        phoneInput.classList.toggle("border-typo-error", !isValid);
        phoneInput.classList.toggle("border-border-input", isValid);
    });

    const phoneInputInternational = document.getElementById("phoneNumberInternational");
    const phoneErrorInternational = document.getElementById("phoneErrorInternational");

    phoneInputInternational.addEventListener("input", function() {

        // Limit the maximum length
        if (phoneInputInternational.value.length > maxLength) {
            phoneInputInternational.value = phoneInputInternational.value.slice(0, maxLength);
        }

        // Validate the input length
        const isValid = phoneInputInternational.value.length >= minLength && phoneInputInternational.value.length <= maxLength;

        // Show or hide the error message
        phoneErrorInternational.style.display = isValid ? "none" : "block";

        // Add or remove error border
        phoneInputInternational.classList.toggle("border-typo-error", !isValid);
        phoneInputInternational.classList.toggle("border-border-input", isValid);
    });
</script>
<script src="https://www.google.com/recaptcha/enterprise.js?render=explicit&onload=onRecaptchaLoadCallback"></script>
<script>
    document.getElementById('identityNumber').addEventListener('input', function() {
        const inputField = this;
        const errorField = document.getElementById('identityNumberError');

        const value = inputField.value;

        if (value.length < 16 || value.length > 16) {
            errorField.style.display = 'block';
            inputField.classList.add('border-typo-error');
            inputField.classList.remove('border-border-input');
        } else {
            errorField.style.display = 'none';
            inputField.classList.remove('border-typo-error');
            inputField.classList.add('border-border-input');
        }
    });

    function onClickGender(key, value) {
        // $('.form-sex').removeClass('success').addClass('active');
        $('.form-sex').text(key).removeClass('text-gray-400').addClass('text-black');
        $('.sex-value').val(value);
        // $('#form-sex').addClass('text-black');

    }
    document.getElementById('filterInput').addEventListener('click', function(event) {
        // Prevent the dropdown from closing when clicking inside the input
        event.stopPropagation();
    });

    document.getElementById('filterInput').addEventListener('keyup', function() {
        let filterValue = this.value.toLowerCase();
        let countryList = document.getElementById('countryList');
        let items = countryList.getElementsByClassName('code-country');

        for (let i = 0; i < items.length; i++) {
            let countryName = items[i].getAttribute('data-name').toLowerCase();
            let countryCode = items[i].getAttribute('data-extension').toLowerCase();
            if (countryName.includes(filterValue) || countryCode.includes(filterValue)) {
                items[i].classList.remove('hidden');
            } else {
                items[i].classList.add('hidden');
            }
        }
    });

    function onRecaptchaLoadCallback() {
        const clientId = grecaptcha.enterprise.render('inline-badge', {
            'sitekey': '{{ config('
            services.recaptcha.key_web ') }}',
            'badge': 'bottomleft',
            'size': 'invisible'
        });
        grecaptcha.enterprise.ready(function() {
            grecaptcha.enterprise.execute(clientId, {
                action: '{{ config('
                services.recaptcha.action ') }}'
            }).then(function(token) {
                if (token) {
                    document.getElementById('recaptcha-domestic').value = token
                    document.getElementById('recaptcha-international').value = token
                }
            });
        });
    }

    @if(session('error'))
    Toastify({
        text: "{{ session('error') }}",
        duration: 3000,
        close: true,
        gravity: "top",
        position: "right",
        style: {
            background: "#D92D20",
        },
    }).showToast();
    @endif

    document.getElementById('domestic-form').addEventListener('submit', function() {
        $('#loading-button').removeClass('hidden');
        $('#submit-button').addClass('hidden');
    });

    document.getElementById('international-form').addEventListener('submit', function() {
        $('.loading-button').removeClass('hidden');
        $('.submit-button').addClass('hidden');
    });

    $(document).ready(function() {
        $("#domesticBtn").on('click', function() {
            $("#international").hide();
            $("#interBtn").removeClass("bg-brand w-52 h-10 rounded-lg text-white text-center py-2").addClass("w-52 h-10 rounded-lg text-brand text-center py-2");

            $("#domestic").show();
            $("#domesticBtn").removeClass("w-52 h-10 rounded-lg text-brand text-center py-2").addClass("bg-brand w-52 h-10 rounded-lg text-white text-center py-2");
        });

        $("#interBtn").on('click', function() {
            $("#domestic").hide();
            $("#domesticBtn").removeClass("bg-brand w-52 h-10 rounded-lg text-white text-center py-2").addClass("w-52 h-10 rounded-lg text-brand text-center py-2");

            $("#international").show();
            $("#interBtn").removeClass("w-52 h-10 rounded-lg text-brand text-center py-2").addClass("bg-brand w-52 h-10 rounded-lg text-white text-center py-2");
        });

        $('.phone-number').on('keydown', function(e) {
            if (isNaN(e.key) && e.key !== 'Backspace') {
                e.preventDefault();
            }
        });

        $('#domestic-form input').on('keyup', function() {
            if ($(this).val() !== '') {
                $('#domestic-form button[type=submit]').prop('disabled', false).removeClass('bg-brand-light').addClass('bg-brand');
            }
        });

        if ($('#domestic-form input').hasClass('border-typo-error')) {
            $('#domestic-form button[type=submit]').prop('disabled', false).removeClass('bg-brand-light').addClass('bg-brand');
        }

        $('#international-form input').on('keyup', function() {
            if ($(this).val() !== '') {
                $('#international-form button[type=submit]').prop('disabled', false).removeClass('bg-brand-light').addClass('bg-brand');
            }
        });

        if ($('#international-form input').hasClass('border-typo-error')) {
            $('#international-form button[type=submit]').prop('disabled', false).removeClass('bg-brand-light').addClass('bg-brand');
        }

        $('#domestic-form .code-country').on('click', function() {
            $('#domestic-form #countryCodeId').val($(this).attr('data-code'));
            $('#domestic-form #selected-country-flag').attr('src', $(this).find('img').attr('src'))
            $('#domestic-form #selected-country-code').text($(this).find('span').text())
        });

        $('#international-form .code-country').on('click', function() {
            $('#international-form #countryCodeId').val($(this).attr('data-code'));
            $('#international-form #selected-country-flag').attr('src', $(this).find('img').attr('src'))
            $('#international-form #selected-country-code').text($(this).find('span').text())
        });

        $('#dateOfBirth').on('change', function() {
            const chosenDate = $(this).val();
            const chosenDateArray = chosenDate.split('/');
            const formatedChosenDate = chosenDateArray[2] + "-" + chosenDateArray[1] + "-" + chosenDateArray[0];
            const parsedChosenDate = new Date(formatedChosenDate).getTime();
            const today = new Date().getTime();
            const dobError = $('.dob-error');

            dobError.text('').addClass('hidden');
            $(this).addClass('border-border-input').removeClass('border-typo-error');

            if (parsedChosenDate > today) {
                $(this).removeClass('border-border-input').addClass('border-typo-error');
                dobError.text('Please choose date from today to the past.').removeClass('hidden');
            }
        });
    });

    const yesterday = () => {
        let date = new Date();
        // date.setDate(date.getDate() - 1);
        date.setDate(date.getDate());
        const day = date.getDate();
        const month = date.getMonth() + 1; // Months are zero-indexed
        const year = date.getFullYear();
        return day + "." + month + "." + year;
    };

    // const datepicker = flatpickr("#dateOfBirth", {
    //     dateFormat: "d/m/Y",
    //     maxDate: yesterday(),
    // });
    //
    // // styling the date picker
    // const calendarContainer = datepicker.calendarContainer;
    // const calendarMonthNav = datepicker.monthNav;
    // const calendarNextMonthNav = datepicker.nextMonthNav;
    // const calendarPrevMonthNav = datepicker.prevMonthNav;
    // const calendarDaysContainer = datepicker.daysContainer;

    // calendarContainer.className = `${calendarContainer.className} bg-white p-4 border border-blue-gray-50 rounded-lg shadow-lg shadow-blue-gray-500/10 font-sans text-sm font-normal text-blue-gray-500 focus:outline-none break-words whitespace-normal`;
    // calendarMonthNav.className = `${calendarMonthNav.className} flex items-center justify-between mb-4 [&>div.flatpickr-month]:-translate-y-3`;
    // calendarNextMonthNav.className = `${calendarNextMonthNav.className} absolute !top-2.5 !right-1.5 h-6 w-6 bg-transparent hover:bg-blue-gray-50 !p-1 rounded-md transition-colors duration-300`;
    // calendarPrevMonthNav.className = `${calendarPrevMonthNav.className} absolute !top-2.5 !left-1.5 h-6 w-6 bg-transparent hover:bg-blue-gray-50 !p-1 rounded-md transition-colors duration-300`;
    // calendarDaysContainer.className = `${calendarDaysContainer.className} [&_span.flatpickr-day]:!rounded-md [&_span.flatpickr-day.selected]:!bg-gray-900 [&_span.flatpickr-day.selected]:!border-gray-900`;
</script>
@endpush