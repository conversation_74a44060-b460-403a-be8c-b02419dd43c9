<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PatientInvoiceSendLogs extends Model
{
    use HasFactory;
    protected $fillable = [
        'patient_id',
        'filename',
        'created_by',
        'url'
    ];

    protected $casts = [
        'created_at' => 'datetime:d/m/Y H:i:s',
        'updated_at' => 'datetime:d/m/Y H:i:s',
    ];

    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }
}
