@extends('landing-page.layouts.operatorPageTemplate')

@section('title', 'Patient')

@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">    
        <div class="d-flex justify-content-between items-center">
                <h4 class="py-3 mb-4"><span class="text-muted fw-light">Send Patient Invoice</h4>
                <div>
                    <button class="btn btn-success" onClick="openModalUpload()">Upload</button>
                </div>
        </div>
        <livewire:operator-bookings.send-patient-invoice.list-logs/>
        <div id="excelUploadModal" class="hidden fixed inset-0 z-50 overflow-y-auto">
            <!-- Backdrop -->
            <div class="fixed inset-0 bg-gray-900/50 backdrop-blur-sm transition-opacity" aria-hidden="true"></div>
          
            <!-- Modal container -->
            <div class="flex min-h-full items-center justify-center p-4 text-center">
              <!-- Modal content -->
              <div class="relative w-full max-w-2xl transform overflow-hidden rounded-xl bg-white text-left shadow-xl transition-all">
                <!-- Header -->
                <div class="flex items-center justify-between border-b border-gray-100 px-6 py-4">
                  <h3 class="text-xl font-semibold text-gray-900">Send Patient Invoice</h3>
                  <button type="button" class="rounded-md p-1.5 text-gray-400 hover:bg-gray-100" onClick="closeModalUpload()">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                 <!-- Body -->
                <livewire:operator-bookings.send-patient-invoice.upload-modal />
              </div>
            </div>
          </div>
    </div>
@endsection

@push('script')
    <script>
        function openModalUpload() {
            document.getElementById('excelUploadModal').classList.remove('hidden');
        }

        function closeModalUpload() {
            document.getElementById('excelUploadModal').classList.add('hidden');
        }
    </script>
@endpush