<?php

namespace App\Livewire\LandingPage\MedicalPackage\Checkout\Component;

use App\Models\AppointmentTemporary;
use App\Models\LandingPage\PackageCategory;
use App\Models\ScheduleDaily;
use App\Repositories\Simrs\Models\AvailabilityDoctorData;
use App\Repositories\Simrs\SimrsRepository;
use App\Services\Simrs\Equipment\ScheduleWeeklyService;
use App\Services\Simrs\Equipment\ScheduleDailyService;
use App\Traits\Calendar;
use Carbon\Carbon;
use Livewire\Attributes\On;
use App\Models\Cms\PublicHoliday;
use Livewire\Component;

class FormDate extends Component
{

    use Calendar;

    public $isShowFormDate = false;
    public $calendarData;
    public $selectedCalendarDataIndex = 0;
    public $selectedDay;
    public $selectedEquipmentId;
    public $showSelectedDay = false;
    public $appointment;
    public $schedules;
    public $scheduleDays;
    public $wireKey;
    public $package_category_simrs_id;
    public array $publicHolidayDates = [];


    public function mount($wireKey, $package_category_id)
    {
        $this->wireKey = $wireKey;
        $this->calendarData = [
            $this->constructFirstMonth(),
            $this->constructSecondMonth(),
            $this->constructThirdMonth()
        ];
        $this->package_category_simrs_id = @PackageCategory::select(['id', 'simrs_id'])->where('id', $package_category_id)->first()->simrs_id;
    }

    public function render()
    {
        return view('livewire.landing-page.medical-package.checkout.component.form-date');
    }

    public function onHandleChangeMonth($isNext)
    {
        if ($isNext) {
            $this->selectedCalendarDataIndex++;
        } else {
            $this->selectedCalendarDataIndex--;
        }
        $this->render();
    }


    public function onHandleSelectedDate($date, $equipmentId)
    {
        $this->selectedDay          = $date;
        $this->selectedEquipmentId  = $equipmentId;
    }

    public function onHandleIsShowFormDate()
    {
        $this->publicHolidayDates = PublicHoliday::pluck('date')->toArray();

        if (!$this->scheduleDays) {
            $data = [
                'service' => $this->package_category_simrs_id,
            ];

            $service = (new ScheduleWeeklyService($data, 3))->call();
            if ($service->status() != 200) {
                session()->flash('error_selected_day', 'Cannot get available schedule, please try again later');
                return;
            }

            $this->scheduleDays = collect($service->data())->pluck('equipment_id', 'full_date')->toArray();
        }

        $this->isShowFormDate = !$this->isShowFormDate;

        if ($this->isShowFormDate) {
            $this->showSelectedDay = false;
        } else {
            $this->selectedDay = null;
            session()->flash('error_selected_day', 'Please select a date');
            $this->dispatch('appointment-schedule-continue-status', 1, false);
        }
    }
    
    public function onHandleApply()
    {
        $this->isShowFormDate   = false;
        $this->showSelectedDay  = true;

        $availableTimes = [];
        $except_slot_ids    = [];
        $maxSlotConfig = str_contains($this->selectedEquipmentId, "MCUE") ? 2 : 4;
        $service = (new ScheduleDailyService([
            'equipment_id'      => $this->selectedEquipmentId,
            'date'              => $this->selectedDay,
            'availability'      => 'Y',
            'except_slot_ids'   => $except_slot_ids
        ]))->call();

        // $service = ScheduleDaily::where('selectedDay', $this->selectedDay)->get();

        // if ($service) {
        if ($service->status() == 200) {
            $times = $service->data();
            // $times = $service;
            foreach ($times as $time) {

                if ($time['date'] == now()->format('Y-m-d')) {
                    $startTime = explode('-', $time['time'])[0];
                    if (Carbon::parse($startTime)->format('H:i') <= Carbon::now()->format('H:i')) {
                        continue;
                    }
                }
                // if($time['selectedDay'] == now()->format('Y-m-d')){
                //     $startTime = explode('-', $time['time'])[0];
                //     if(Carbon::parse($startTime)->format('H:i') <= Carbon::now()->format('H:i')){
                //         continue;
                //     }
                // }
                // $startTime = trim(explode('-', $time['time'])[0]);
                // $appointments = AppointmentTemporary::whereDate('appointment_date', Carbon::parse($this->selectedDay)->addDay()->format('Y-m-d'))
                // ->where('appointment_time',$startTime)->get();

                $filteredAppoinments = array_filter($time['appointments'], function ($e) {
                    return $e['appointment_status'] != "X";
                });

                $temp           = [
                    'time'          => $time['time'],
                    'slot_id'       => $time['slot_id'],
                    // 'slot_remaining' => intval(4 - count($appointments)),
                    'slot_remaining' => intval($maxSlotConfig - count($filteredAppoinments)),
                ];
                $availableTimes[] = $temp;
            }
        }

        $this->dispatch(
            'medical-package-updated-form-data',
            [
                'selected_visit_date'                   => $this->selectedDay,
                'selected_equipment_id'                 => $this->selectedEquipmentId,
                'selected_package_category_simrs_id'    => $this->package_category_simrs_id,
                'available_times'                       => $availableTimes
            ],
            wireKey: $this->wireKey
        );
    }

    public function onHandleCancel()
    {
        $this->isShowFormDate   = false;
        $this->selectedDay      = null;
        $this->dispatch('medical-package-updated-form-data', [
            'selected_visit_date'                   => 'clear'
        ], wireKey: $this->wireKey);
        session()->flash('error_selected_day', 'Please select a date');
    }

    #[On('medical_package_error_validation')]
    public function onListenErrorValidation()
    {
        if (!$this->selectedDay) {
            session()->flash('error_selected_day-' . $this->wireKey, 'Please select a date');
        }
    }

    #[On('medical_package_error_slot_not_enough')]
    public function onListenErrorSlotNotEnough($data)
    {
        if ($data[0]['equipment_id'] == $this->selectedEquipmentId && $data[0]['date'] == $this->selectedDay) {
            session()->flash('error_selected_day-' . $this->wireKey, 'The selected slot is unavailable. Kindly choose an alternative date.');
        }
    }
}
