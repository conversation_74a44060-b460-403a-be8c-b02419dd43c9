<style>
    .nav-header {
        text-decoration: none;
        position: relative;
        transition: color 0.3s ease;
        width: fit-content;
    }

    .nav-header.scrolled {
        color: #475467;
    }

    .nav-header::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: -3px;
        width: 0;
        height: 2px;
        background-color: white;
        transition: width 0.3s ease, transform 0.3s ease;
        transform-origin: center;
    }

    .nav-header.scrolled:hover {
        color: #0D4D8B;
    }

    .nav-header.scrolled:hover::after {
        color: #0D4D8B;
        background-color: #0D4D8B;
        width: 100%;
        transform: translateX(-50%) scaleX(1);
    }

    .nav-header:hover {
        text-decoration: none;
    }

    .nav-header:hover::after {
        width: 100%;
        transform: translateX(-50%) scaleX(1);
    }

    #patientCareDropdownContent,
    #internationalPartnershipsCareDropdownContent,
    #medicalTravelDropdownContent {
        left: 50%;
        transform: translateX(-50%);
    }

    #patientCareDropdownContent .itemDropdown,
    #internationalPartnershipsCareDropdownContent .itemDropdown,
    #medicalTravelDropdownContent .itemDropdown {
        display: block;
        text-decoration: none;
        width: fit-content;
        color: #475467;
        position: relative;
        margin: 24px 32px;
    }

    #patientCareDropdownContent .itemDropdown:hover,
    #internationalPartnershipsCareDropdownContent .itemDropdown:hover,
    #medicalTravelDropdownContent .itemDropdown:hover {
        color: #0D4D8B;
    }

    #patientCareDropdownContent .itemDropdown::after,
    #internationalPartnershipsCareDropdownContent .itemDropdown::after,
    #medicalTravelDropdownContent .itemDropdown::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: -3px;
        width: 0;
        height: 2px;
        background-color: #0D4D8B;
        transition: width 0.3s ease, transform 0.3s ease;
        transform-origin: center;
    }

    #patientCareDropdownContent .itemDropdown:hover::after,
    #internationalPartnershipsCareDropdownContent .itemDropdown:hover::after,
    #medicalTravelDropdownContent .itemDropdown:hover::after {
        width: 100%;
        transform: translateX(-50%) scaleX(1);
    }

    .linguise_current_lang{
        border:none !important;
    }
    .lccaret.top {
        display: none !important;
    }
    #dropdown .linguise_switcher_dropdown li {
        display: block;
        padding-left: 0; /* Default for mobile */
        min-width: 130px;
        border-bottom: none !important;
    }

    /* Apply padding-left on medium (768px) and up */
    @media (min-width: 768px) {
        #dropdown .linguise_switcher_dropdown li {
            padding-left: 60px;
        }
    }
    .linguise_lang_name {
        color: inherit !important; /* or use specific color like black */
    }
    .linguise_lang_item {
        background-color: black !important;
    }

    .linguise_lang_item:hover {
        background-color: black !important;
    }

    .linguise_lang_item:hover .linguise_lang_name {
        color: white !important;
    }
</style>

<header class="hidden header xl:block fixed top-0 left-0 w-screen py-2 px-40 z-30 text-white" style="background-image: linear-gradient(180deg, #000000c7, #0000006e 80%, transparent);">
    <div class="flex flex-col space-y-4 justify-center">
        <div class="flex justify-between h-11 items-center">
            <a href="/" class="bg-red items-center mt-2">
                <img class="ihcIcon h-[70px]" src="{{ asset('assets/header/ihc-white.png') }}" alt="ihc-logo">
            </a>
            <div class="flex justify-between text-sm font-weight-400 w-[658px] ml-8">
                <a href="/about-us" class="nav-header">About Us</a>
                {{-- <a href="/patient-centered-care/center-of-excellence" class="block itemDropdown">Center of--}}
                {{-- Excellence</a>--}}
                <div class="relative">
                    <div class="nav-header hover:cursor-pointer" id="patientCareDropdownTrigger">Patient-Centered Care
                    </div>
                    <div class="absolute z-10 hidden bg-white shadow-md mt-2 rounded-2xl w-[320px]"
                        id="patientCareDropdownContent">
                        <a href="/center-of-excellence" class="block itemDropdown">Centers of
                            Excellence</a>
                        <a href="/segara-integrated-clinic"
                            class="block itemDropdown">Segara Integrated Clinic</a>
                        <a href="/emergency-medical-services-ems" class="block itemDropdown">Emergency Department</a>
                        <a href="/inpatient-rooms" class="block itemDropdown">Inpatient Rooms (Wards)</a>
                        <a href="/insurance" class="block itemDropdown">Insurance</a>
                        <!-- <a href="#" class="block itemDropdown cursor-default" style="color: #ababab !important;">Medicine
                            Inquiries</a>
                        <a href="/patient-centered-care/air-ground-ambulance-services" class="block itemDropdown">Air &
                            Ground
                            Ambulance Services</a> -->
                    </div>
                </div>
                <div class="relative">
                    <div class="nav-header cursor-not-allowed opacity-50" id="medicalTravelDropdownTrigge">Medical Travel
                    </div>
                    {{-- <div class="nav-header cursor-not-allowed opacity-50" id="medicalTravelDropdownTrigger">Medical Travel
                    </div> --}}
                    <div class="absolute z-10 hidden bg-white shadow-md mt-2 rounded-2xl w-[380px] pointer-events-none opacity-50"
                        id="medicalTravelDropdownContent">
                        <a href="/medical-travel/medical-tourism" class="block itemDropdown">Medical Tourism</a>
                        <a href="/medical-travel/medical-travel-guide" class="block itemDropdown">Medical Travel Guide</a>
                        <a href="/medical-travel/medical-coordination-office" class="block itemDropdown">Medical Coordination Office</a>
                        <a href="/medical-travel/medical-services-cost-financing-information" class="block itemDropdown">Medical Services Cost & Financing Information</a>
                        <a href="/medical-travel/fast-track-pay-services" class="block itemDropdown">Fast Track Pay Services</a>
                        <a href="/medical-travel/associated-health-insurance-companies" class="block itemDropdown">Associated Health Insurance Companies</a>
                    </div>
                </div>
                {{-- <div class="relative">
                    <div class="nav-header hover:cursor-pointer" id="medicalTravelDropdownTrigger">Medical Travel
                    </div>
                    <div class="absolute z-10 hidden bg-white shadow-md mt-2 rounded-2xl w-[380px]"
                        id="medicalTravelDropdownContent">
                        <a href="/medical-travel/medical-tourism" class="block itemDropdown">Medical Tourism</a>
                        <a href="/medical-travel/medical-travel-guide" class="block itemDropdown">Medical Travel
                            Guide</a>
                        <a href="/medical-travel/medical-coordination-office" class="block itemDropdown">Medical
                            Coordination Office</a>
                        <a href="/medical-travel/medical-services-cost-financing-information"
                            class="block itemDropdown">Medical Services Cost & Financing Information</a>
                        <a href="/medical-travel/fast-track-pay-services" class="block itemDropdown">Fast Track Pay
                            Services</a>
                        <a href="/medical-travel/associated-health-insurance-companies"
                            class="block itemDropdown">Associated Health Insurance Companies</a>
                    </div>
                </div> --}}
                {{-- <a href="{{route('ihc.index')}}" class="nav-header">IHC Hospitals</a>--}}
                <a href="/career" class="flex gap-1 nav-header">
                    Join Our Team
                </a>
                {{-- <a href="tel:150442" class="flex gap-1 nav-header">
                    <span class="">
                        {!! file_get_contents('assets/header/emergency.svg') !!}
                    </span>
                    Emergency 150442
                </a> --}}
            </div>
            <div class="flex justify-between items-center gap-4">
                <div>[linguise]</div>
                <a href="{{route('medical_packages.checkout', \Illuminate\Support\Str::uuid())}}" class="hover:cursor-pointer">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" id="cart-empty">
                        <path class="cartIcon" d="M3.5 4H4.42379C4.76358 4 5.06092 4.22844 5.14847 4.55675L5.40393 5.51473M7 11.5C5.89543 11.5 5 12.3954 5 13.5H15.5M7 11.5H14.4788C15.2262 9.96624 15.8785 8.37757 16.4278 6.74167C13.2534 5.93097 9.92701 5.5 6.5 5.5C6.13347 5.5 5.76809 5.50493 5.40393 5.51473M7 11.5L5.40393 5.51473M6 15.5C6 15.7761 5.77614 16 5.5 16C5.22386 16 5 15.7761 5 15.5C5 15.2239 5.22386 15 5.5 15C5.77614 15 6 15.2239 6 15.5ZM14.5 15.5C14.5 15.7761 14.2761 16 14 16C13.7239 16 13.5 15.7761 13.5 15.5C13.5 15.2239 13.7239 15 14 15C14.2761 15 14.5 15.2239 14.5 15.5Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="white" id="cart-exists" class="cartIcon hidden">
                        <path class="cartIcon" d="M10.9167 11.75H11.9752C12.671 11.75 13.0188 11.75 13.2807 11.9388C13.5426 12.1275 13.6526 12.4575 13.8726 13.1175L14.2501 14.25" stroke="white" stroke-width="1.5" stroke-linecap="round" />
                        <path class="cartIcon" d="M22.5834 22.5834H14.7092C14.0734 22.5834 13.6244 21.9605 13.8254 21.3573V21.3573C14.079 20.5965 14.791 20.0834 15.5929 20.0834H20.0834" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        <path class="cartIcon" d="M19.6679 20.0833H17.4123C16.436 20.0833 15.9479 20.0833 15.5383 19.8872C15.374 19.8085 15.2213 19.7078 15.0843 19.5878C14.7426 19.2886 14.5504 18.8399 14.1658 17.9426C13.5516 16.5094 13.2445 15.7928 13.45 15.2331C13.53 15.0151 13.6594 14.8188 13.8282 14.6594C14.2618 14.25 15.0414 14.25 16.6007 14.25H20.4467C22.2548 14.25 23.1589 14.25 23.6049 14.7371C23.7509 14.8967 23.8608 15.0859 23.9268 15.2919C24.1285 15.9208 23.68 16.7057 22.7829 18.2757C22.338 19.0541 22.1156 19.4434 21.7744 19.6944C21.6579 19.7801 21.5323 19.853 21.4001 19.9116C21.0128 20.0833 20.5645 20.0833 19.6679 20.0833Z" stroke="white" stroke-width="1.5" stroke-linecap="round" />
                        <circle cx="22.1666" cy="24.6667" r="0.833333" fill="#667085" />
                        <ellipse cx="15.5001" cy="24.6667" rx="0.833333" ry="0.833333" fill="#667085" />
                        <circle cx="24" cy="12" r="4.75" fill="#F04438" stroke="white" stroke-width="1.5" />
                    </svg>

                </a>
                @if (Session::has('user_id'))
                    <div class="hs-dropdown relative inline-flex">
                    <button id="hs-dropdown-default" type="button" class="profileBtn hs-dropdown-toggle p-1 rounded-full inline-flex items-center gap-x-2 text-sm font-medium border border-white text-gray-800 shadow-sm focus:outline-none disabled:opacity-50 disabled:pointer-events-none" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                        {!! file_get_contents('assets/header/bars.svg') !!}
                        <div
                            class="avatarBorder border border-1 border-white rounded-full mr-1.5 text-black bg-white text-12-18 w-6 h-6 text-center py-0 leading-6">
                            {{ strtoupper(substr(Session::get('user_name'), 0, 1)) }}
                        </div>
                    </button>
                    <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white shadow-md rounded-lg mt-2 after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4 before:absolute before:-top-4 before:start-0 before:w-full" role="menu" aria-orientation="vertical" aria-labelledby="hs-dropdown-default">
                        <div class="p-1 space-y-0.5">
                            <div class="flex py-3 first:pt-3 last:pb-0 mb-1">
                                <div class="flex-none pl-3">
                                    <div
                                        class="rounded-full w-10 h-10 bg-gradient-to-tr from-primary-blue-40 to-primary-blue-120 text-center text-2xl text-white pt-1">
                                        {{ strtoupper(substr(Session::get('user_name'), 0, 1)) }}
                                    </div>
                                </div>
                                <div class="flex-1 text-black text-14-20 pl-3">
                                    <p class="text-gray-500">Logged in as</p>
                                    <p>{{ Session::get('user_name') }}</p>
                                </div>
                            </div>
                            <div class="py-2 first:pt-0 last:pb-0">
                                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"
                                    href="{{ route('profile.index') }}">
                                    {!! file_get_contents('assets/header/user-group.svg') !!}
                                    View Profile
                                </a>
                                {{-- <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"--}}
                                {{-- href="#">--}}
                                {{-- {!! file_get_contents('assets/header/document-plus.svg') !!}--}}
                                {{-- Medical Resume--}}
                                {{-- </a>--}}
                                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"
                                    href="{{route('profile.mybook.index')}}">
                                    {!! file_get_contents('assets/header/calendar-days.svg') !!}
                                    My Bookings
                                </a>
                            </div>
                            <div class="py-2 first:pt-0 last:pb-0">
                                <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-error-50 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"
                                    href="{{ route('login.public.logout') }}">
                                    {!! file_get_contents('assets/header/logout.svg') !!}
                                    Log out
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @else
                <a href="{{ route('login.public.index') }}">
                    <div class="flex h-9 w-24 items-center justify-between border-[1px] rounded-full text-xs">
                        <span class="ml-3">Log In</span>
                        <div>{!! file_get_contents('assets/header/ava.svg') !!}</div>
                    </div>
                </a>
                @endif
            </div>
        </div>
    </div>
    <div class="flex flex-col h-10 mt-4 w-full gap-3">
        <div class="bg-[#D0D5DD] h-[1px] rounded-full">
        </div>
        <div class="flex items-center justify-center">
            <div class="flex justify-between text-sm font-weight-400 w-[658px] ml-8 gap-2">
                <a href="{{ route('doctors.index') }}" class="nav-header">Find a Doctor</a>
                <a href="{{ route('appointments.book', [
                    'uuid'          => \Illuminate\Support\Str::uuid(),
                    'doctor_uuid'   => 'NO',
                    'type'          => 1
                ]) }}" class="nav-header">Clinic Appointment</a>
                {{--<a href="/medical-packages/category/health-screening-basic" class="nav-header">Packages</a>--}}
                <div class="hs-dropdown relative inline-flex">
                    <div id="hs-dropdown-default" type="button" class="nav-header hs-dropdown-toggle hover:cursor-pointer" onclick="onHandleOpenModalPackage()">
                     Packages
                    </div>
                    <div id="dropdownContainer" class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white shadow-md rounded-lg p-2 mt-2 after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4 before:absolute before:-top-4 before:start-0 before:w-full linguise_package_type" aria-labelledby="hs-dropdown-default">--}}
                    </div>
                </div>
                <!-- <div class="flex justify-between text-sm font-weight-400 w-[658px] ml-8 gap-2"> -->
                <div class="relative">
                    <div class="nav-header hover:cursor-pointer" id="internationalPartnershipsCareDropdownTrigger">International Partnerships
                    </div>
                    <div class="absolute z-10 hidden bg-white shadow-md mt-2 rounded-2xl w-[320px]"
                        id="internationalPartnershipsCareDropdownContent">
                        <a href="/icon-cancer-centre" class="block itemDropdown">Icon Cancer Centre</a>
                        <a href="/innoquest"
                            class="block itemDropdown">Innoquest</a>
                        <a href="/sapporo-cardiovascular-clinic-scvc"
                            class="block itemDropdown">Bali Sapporo Cardiovascular Center (BSCC)</a>
                        <!-- <a href="#" class="block itemDropdown cursor-default" style="color: #ababab !important;">Medicine
                            Inquiries</a>
                        <a href="#" style="color: #ababab !important;" class="block itemDropdown cursor-default">Air &
                            Ground
                            Ambulance Services</a> -->
                    </div>
                </div>
                <!-- </div> -->
                <a href="/contact-us" class="nav-header">Contact Us</a>
            </div>
        </div>
    </div>
</header>
<header class="xl:hidden fixed top-0 left-0 flex flex-wrap z-50 w-screen text-sm py-4 header text-white">
    <nav class="max-w-[85rem] w-full mx-auto px-4 " aria-label="Global">
        <div class="flex justify-end">
            @if(!in_array($platform, ['mobile']))
            {{-- <a href="/career" class="flex gap-1 nav-header text-white">
                Join Our Team
            </a> --}}
            {{-- <a href="tel:150442" class="flex gap-1 nav-header text-white">
                <span class="">
                    {!! file_get_contents('assets/header/emergency.svg') !!}
                </span>
                Emergency 150442
            </a> --}}
            @endif
        </div>
        <div class="flex items-center justify-between mt-2">
            <a href="/">
                <img class="ihcIcon h-[60px]" src="{{ asset('assets/header/ihc-white.png') }}" alt="ihc-logo">
            </a>
            @if(!in_array($platform, ['mobile']))
            <div class="flex flex-row gap-1 items-center">
                <a href="{{route('medical_packages.checkout', \Illuminate\Support\Str::uuid())}}" class="hover:cursor-pointer">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" id="cart-empty">
                        <path class="cartIcon" d="M3.5 4H4.42379C4.76358 4 5.06092 4.22844 5.14847 4.55675L5.40393 5.51473M7 11.5C5.89543 11.5 5 12.3954 5 13.5H15.5M7 11.5H14.4788C15.2262 9.96624 15.8785 8.37757 16.4278 6.74167C13.2534 5.93097 9.92701 5.5 6.5 5.5C6.13347 5.5 5.76809 5.50493 5.40393 5.51473M7 11.5L5.40393 5.51473M6 15.5C6 15.7761 5.77614 16 5.5 16C5.22386 16 5 15.7761 5 15.5C5 15.2239 5.22386 15 5.5 15C5.77614 15 6 15.2239 6 15.5ZM14.5 15.5C14.5 15.7761 14.2761 16 14 16C13.7239 16 13.5 15.7761 13.5 15.5C13.5 15.2239 13.7239 15 14 15C14.2761 15 14.5 15.2239 14.5 15.5Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="white" id="cart-exists" class="cartIcon hidden">
                        <path class="cartIcon" d="M10.9167 11.75H11.9752C12.671 11.75 13.0188 11.75 13.2807 11.9388C13.5426 12.1275 13.6526 12.4575 13.8726 13.1175L14.2501 14.25" stroke="white" stroke-width="1.5" stroke-linecap="round" />
                        <path class="cartIcon" d="M22.5834 22.5834H14.7092C14.0734 22.5834 13.6244 21.9605 13.8254 21.3573V21.3573C14.079 20.5965 14.791 20.0834 15.5929 20.0834H20.0834" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        <path class="cartIcon" d="M19.6679 20.0833H17.4123C16.436 20.0833 15.9479 20.0833 15.5383 19.8872C15.374 19.8085 15.2213 19.7078 15.0843 19.5878C14.7426 19.2886 14.5504 18.8399 14.1658 17.9426C13.5516 16.5094 13.2445 15.7928 13.45 15.2331C13.53 15.0151 13.6594 14.8188 13.8282 14.6594C14.2618 14.25 15.0414 14.25 16.6007 14.25H20.4467C22.2548 14.25 23.1589 14.25 23.6049 14.7371C23.7509 14.8967 23.8608 15.0859 23.9268 15.2919C24.1285 15.9208 23.68 16.7057 22.7829 18.2757C22.338 19.0541 22.1156 19.4434 21.7744 19.6944C21.6579 19.7801 21.5323 19.853 21.4001 19.9116C21.0128 20.0833 20.5645 20.0833 19.6679 20.0833Z" stroke="white" stroke-width="1.5" stroke-linecap="round" />
                        <circle cx="22.1666" cy="24.6667" r="0.833333" fill="#667085" />
                        <ellipse cx="15.5001" cy="24.6667" rx="0.833333" ry="0.833333" fill="#667085" />
                        <circle cx="24" cy="12" r="4.75" fill="#F04438" stroke="white" stroke-width="1.5" />
                    </svg>
                </a>
                @if (Session::has('user_id'))
                    <div class="hs-dropdown relative inline-flex">
                        <button id="hs-dropdown-default" type="button" class="profileBtn hs-dropdown-toggle p-1 rounded-full inline-flex items-center gap-x-2 text-sm font-medium border border-white text-gray-800 shadow-sm focus:outline-none disabled:opacity-50 disabled:pointer-events-none" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                            {!! file_get_contents('assets/header/bars.svg') !!}
                            <div
                                class="avatarBorder border border-1 border-white rounded-full mr-1.5 text-black bg-white text-12-18 w-6 h-6 text-center py-0 leading-6">
                                {{ strtoupper(substr(Session::get('user_name'), 0, 1)) }}
                            </div>
                        </button>
                        <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white shadow-md rounded-lg mt-2 after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4 before:absolute before:-top-4 before:start-0 before:w-full" role="menu" aria-orientation="vertical" aria-labelledby="hs-dropdown-default">
                            <div class="p-1 space-y-0.5">
                                <div class="flex py-3 first:pt-3 last:pb-0 mb-1">
                                    <div class="flex-none pl-3">
                                        <div
                                            class="rounded-full w-10 h-10 bg-gradient-to-tr from-primary-blue-40 to-primary-blue-120 text-center text-2xl text-white pt-1">
                                            {{ strtoupper(substr(Session::get('user_name'), 0, 1)) }}
                                        </div>
                                    </div>
                                    <div class="flex-1 text-black text-14-20 pl-3">
                                        <p class="text-gray-500">Logged in as</p>
                                        <p>{{ Session::get('user_name') }}</p>
                                    </div>
                                </div>
                                <div class="py-2 first:pt-0 last:pb-0">
                                    <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"
                                        href="{{ route('profile.index') }}">
                                        {!! file_get_contents('assets/header/user-group.svg') !!}
                                        View Profile
                                    </a>
                                    {{-- <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"--}}
                                    {{-- href="#">--}}
                                    {{-- {!! file_get_contents('assets/header/document-plus.svg') !!}--}}
                                    {{-- Medical Resume--}}
                                    {{-- </a>--}}
                                    <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"
                                        href="{{route('profile.mybook.index')}}">
                                        {!! file_get_contents('assets/header/calendar-days.svg') !!}
                                        My Bookings
                                    </a>
                                </div>
                                <div class="py-2 first:pt-0 last:pb-0">
                                    <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-error-50 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"
                                        href="{{ route('login.public.logout') }}">
                                        {!! file_get_contents('assets/header/logout.svg') !!}
                                        Log out
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <a href="{{ route('login.public.index') }}">
                        <div class="flex h-9 w-24 items-center justify-between border-[1px] rounded-full text-xs tex">
                            <span class="ml-3">Log In</span>
                            <div>{!! file_get_contents('assets/header/ava.svg') !!}</div>
                        </div>
                    </a>
                @endif
                <div class="">
                    <button type="button"
                        class="hs-collapse-toggle p-2 inline-flex justify-center items-center gap-x-2 rounded-lg text-gray-800 shadow-sm disabled:opacity-50 disabled:pointer-events-none"
                        data-hs-collapse="#navbar-with-mega-menu" aria-controls="navbar-with-mega-menu"
                        aria-label="Toggle navigation">
                        <svg class="hs-collapse-open:hidden flex-shrink-0 size-4 button-header"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="white" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round">
                            <line x1="3" x2="21" y1="6" y2="6" />
                            <line x1="3" x2="21" y1="12" y2="12" />
                            <line x1="3" x2="21" y1="18" y2="18" />
                        </svg>
                        <svg class="hs-collapse-open:block hidden flex-shrink-0 size-4 button-header"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="white" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round">
                            <path d="M18 6 6 18" />
                            <path d="m6 6 12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            @endif
        </div>
        <div class="flex flex-row w-full justify-end -mt-4">
            <div>[linguise]</div>
        </div>
        <div id="navbar-with-mega-menu"
            class="hs-collapse hidden overflow-hidden transition-all duration-300 basis-full grow bg-white -mx-4 px-4 mt-4">
            <div class="flex flex-col gap-5 py-5 overflow-scroll max-h-96">
                <a class="font-medium text-neutral-40" href="{{route('aboutUs.index')}}">About Us</a>
                <div class="hs-dropdown [--strategy:static] [--adaptive:none]">
                    <button id="hs-mega-menu-basic-dr" type="button"
                        class="flex items-center w-full text-gray-600 hover:text-gray-400 font-medium">
                        Patient-Centered Care
                        <svg class="ms-1 flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24"
                            height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="m6 9 6 6 6-6" />
                        </svg>
                    </button>

                    <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms]  hs-dropdown-open:opacity-100 opacity-0 z-10 bg-white  rounded-lg p-2 before:absolute top-full before:-top-5 before:start-0 before:w-full before:h-5 hidden"
                        style="">
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/center-of-excellence">
                            Centers of Excellence
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/segara-integrated-clinic">
                            Segara Integrated Clinic
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/emergency-medical-services-ems">
                            Emergency Department
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/inpatient-rooms">
                            Inpatient Rooms (Wards)
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/insurance">
                            Insurance
                        </a>
                        <!-- <a class="flex cursor-not-allowed items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-400 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="#">
                            Medicine
                            Inquiries
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/patient-centered-care/air-ground-ambulance-services">
                            Air &
                            Ground
                            Ambulance Services
                        </a> -->
                    </div>
                </div>
                <div class="hs-dropdown [--strategy:static] [--adaptive:none]">
                    <button id="hs-mega-menu-basic-dr" type="button"
                        class="flex items-center w-full text-gray-600 hover:text-gray-400 font-medium" disabled>
                        Medical Travel
                        <svg class="ms-1 flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24"
                            height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="m6 9 6 6 6-6" />
                        </svg>
                    </button>
                    <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms]  hs-dropdown-open:opacity-100 opacity-0 z-10 bg-white  rounded-lg p-2 before:absolute top-full before:-top-5 before:start-0 before:w-full before:h-5 hidden"
                        style="">
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/medical-travel/medical-tourism">
                            Medical Tourism
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/medical-travel/medical-travel-guide">
                            Medical Travel Guide
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/medical-travel/medical-coordination-office">
                            Medical Coordination Office
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/medical-travel/medical-services-cost-financing-information">
                            Medical Services Cost & Financing Information
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/medical-travel/fast-track-pay-services">
                            Fast Track Pay Services
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/medical-travel/associated-health-insurance-companies">
                            Associated Health Insurance Companies
                        </a>
                    </div>
                </div>
                <a class="font-medium text-neutral-40" href="{{route('articles.index')}}">Blog</a>
                <a href="{{ route('doctors.index') }}" class="font-medium text-neutral-40">Find a Doctor</a>
                <a href="{{ route('appointments.book', [
                    'uuid'          => \Illuminate\Support\Str::uuid(),
                    'doctor_uuid'   => 'NO',
                    'type'          => 1
                ]) }}" class="font-medium text-neutral-40">Clinic Appointment</a>
                {{-- <a href="/medical-packages/category/mcu-basic" class="font-medium text-neutral-40">Packages</a> --}}
                <div class="hs-dropdown [--strategy:static] [--adaptive:none]">
                    <button id="hs-mega-menu-basic-dr" type="button"
                        class="flex items-center w-full text-gray-600 hover:text-gray-400 font-medium">
                        International Partnerships
                        <svg class="ms-1 flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24"
                            height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="m6 9 6 6 6-6" />
                        </svg>
                    </button>

                    <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms]  hs-dropdown-open:opacity-100 opacity-0 z-10 bg-white  rounded-lg p-2 before:absolute top-full before:-top-5 before:start-0 before:w-full before:h-5 hidden"
                        style="">
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/icon-cancer-centre">
                            Icon Cancer Centre
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/innoquest">
                            Innoquest
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500"
                            href="/sapporo-cardiovascular-clinic-scvc">
                            Bali Sapporo Cardiovascular Center (BSCC)
                        </a>
                    </div>
                </div>

                <div class="hs-dropdown [--strategy:static] [--adaptive:none]">
                <button id="hs-mega-menu-basic-dr" type="button"
                class="flex items-center w-full text-gray-600 hover:text-gray-400 font-medium" onclick="onHandleOpenModalPackageMobile()">
                Packages
                <svg class="ms-1 flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24"
                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m6 9 6 6 6-6" />
                </svg>
                </button>

                <div id="dropdownContainerMobile"
                    class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white p-2 mt-2 after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4 before:absolute before:-top-4 before:start-0 before:w-full" aria-labelledby="hs-dropdown-default">
                </div>
            </div>
            <a href="#" class="font-medium text-neutral-40">Clinics & Centers</a>
            <a href="{{route('contactUs.index')}}" class="font-medium text-neutral-40">Contact Us</a>
            @if (Session::has('user_id'))
            {{-- <div class="hs-dropdown [--strategy:static] [--adaptive:none]">--}}
            {{-- <button id="hs-mega-menu-basic-dr" type="button"--}}
            {{-- class="flex items-center w-full text-gray-600 hover:text-gray-400 font-medium">--}}
            {{-- <span class="font-medium text-neutral-40">Logged in as {{ Session::get('user_name') }}</span>--}}
            {{-- <svg class="ms-1 flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24"--}}
            {{-- height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"--}}
            {{-- stroke-width="2" stroke-linecap="round" stroke-linejoin="round">--}}
            {{-- <path d="m6 9 6 6 6-6" />--}}
            {{-- </svg>--}}
            {{-- </button>--}}

            {{-- <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms]  hs-dropdown-open:opacity-100 opacity-0 z-10 bg-white  rounded-lg p-2 before:absolute top-full before:-top-5 before:start-0 before:w-full before:h-5 hidden"--}}
            {{-- style="">--}}
            {{-- <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"--}}
            {{-- href="{{ route('profile.index') }}">--}}
            {{-- {!! file_get_contents('assets/header/user-group.svg') !!}--}}
            {{-- View Profile--}}
            {{-- </a>--}}
            {{-- <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"--}}
            {{-- href="#">--}}
            {{-- {!! file_get_contents('assets/header/document-plus.svg') !!}--}}
            {{-- Medical Resume--}}
            {{-- </a>--}}
            {{-- <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"--}}
            {{-- href="{{route('profile.mybook.index')}}">--}}
            {{-- {!! file_get_contents('assets/header/calendar-days.svg') !!}--}}
            {{-- My Bookings--}}
            {{-- </a>--}}
            {{-- <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-error-50 hover:border-gray-100 hover:border hover:bg-gray-50 focus:outline-none focus:bg-gray-100 white:text-gray-400 white:hover:bg-gray-700 white:hover:text-gray-300 white:focus:bg-gray-700"--}}
            {{-- href="{{ route('login.public.logout') }}">--}}
            {{-- {!! file_get_contents('assets/header/logout.svg') !!}--}}
            {{-- Log out--}}
            {{-- </a>--}}
            {{-- </div>--}}
            {{-- </div>--}}
            @else
            <a href="/login" class="font-medium text-neutral-40">Login</a>
            @endif
        </div>
        </div>
    </nav>
</header>

@if (in_array(explode('/', request()->path())[0], [
'promos',
'articles',
'our-partner',
'livewire',
'profile',
'contact-us',
]) ||
in_array(\Illuminate\Support\Facades\Route::currentRouteName(), [
'appointments.book',
'patientCenteredCare.healthcare-at-home',
'patientCenteredCare.air-ground-ambulance-services',
'patientCenteredCare.inpatient-rooms',
'patientCenteredCare.medicine-inquiries',
'patientCenteredCare.patient-privilege',
'doctors.show',
'medical_packages.show',
'medical_packages.checkout',
'medical_packages.success',
'tnc.index',
'privacy-policy.index',
'cookie-policy.index',
'faq.index'
]))
@push('script')
<script>
    var navHeader = $('.nav-header');
    $('.header').addClass(
        'bg-white transition-all duration-500 ease-in-out text-[#475467] shadow-md');
    $('.header').removeClass('text-white');
    $('#iconSearch').removeClass('fill-white');
    $('#iconSearch').addClass("fill-[#0D4D8B] transition-all duration-500 ease-in-out")
    $('#iconFill').addClass("fill-[#EAFFCC] transition-all duration-500 ease-in-out")
    $('.header').css('background', '');
    $('.emergencyIcon').css("fill", "red")
    $('.emergencyIcon').addClass("transition-all duration-500 ease-in-out")
    navHeader.addClass('scrolled');
    $(".ihcIcon").attr("src", "{{ asset_gcs('public/assets/common/dcaed5ae-7c91-42d1-85ba-86ee245fc7fa.png') }}");
    $('.ihcIcon').addClass("transition-all duration-500 ease-in-out")
    $('.cartIcon').css("stroke", "#667085")
    $('.cartIcon').addClass("transition-all duration-500 ease-in-out")
    $('.avaHeader').css("fill", "#D9D9D9")
    $('.avaHeader').addClass("transition-all duration-500 ease-in-out")
    $('.barsIcon').css("fill", "#667085")
    $('.barsIcon').addClass("transition-all duration-500 ease-in-out")
    $('.profileBtn').removeClass('border-white');
    $('.profileBtn').addClass('border-neutral-40');
    $('.avatarBorder').removeClass('border-white');
    $('.avatarBorder').addClass('border-neutral-40');
    $('.button-header').attr('stroke', '#1D2939');
    // When the link is clicked
    $('#patientCareDropdownTrigger').click(function() {
        $('#patientCareDropdownContent').toggle();
    });
    $('#internationalPartnershipsCareDropdownTrigger').click(function() {
        $('#internationalPartnershipsCareDropdownContent').toggle();
    });
    $('#medicalTravelDropdownTrigger').click(function() {
        $('#medicalTravelDropdownContent').toggle();
    });
    $(document).click(function(event) {
        if (!$(event.target).closest('#patientCareDropdownTrigger').length &&
            !$(event.target).closest('#patientCareDropdownContent').length) {
            $('#patientCareDropdownContent').hide();
        }

        if (!$(event.target).closest('#internationalPartnershipsCareDropdownTrigger').length &&
            !$(event.target).closest('#internationalPartnershipsCareDropdownContent').length) {
            $('#internationalPartnershipsCareDropdownContent').hide();
        }
        if (!$(event.target).closest('#medicalTravelDropdownTrigger').length &&
            !$(event.target).closest('#medicalTravelDropdownContent').length) {
            $('#medicalTravelDropdownContent').hide();
        }
    });
</script>
@endpush()
@else
@push('script')
<script>
    $(document).ready(function() {
        $('.linguise_lang_item').addClass(
            'bg-black hover:bg-black hover:text-black');
        $(window).scroll(function() {
            var scroll = $(window).scrollTop();
            var navHeader = $('.nav-header');
            if (scroll > 150) {
                $('.header').addClass(
                    'bg-white transition-all duration-500 ease-in-out text-[#475467] shadow-md');
                $('.linguise_lang_item').addClass(
                    'bg-white transition-all duration-500 ease-in-out text-[#475467] shadow-md');
                $('.header').removeClass('text-white');
                $('#iconSearch').removeClass('fill-white');
                $('#iconSearch').addClass("fill-[#0D4D8B] transition-all duration-500 ease-in-out")
                $('#iconFill').addClass("fill-[#EAFFCC] transition-all duration-500 ease-in-out")
                $('.header').css('background', '');
                $('.emergencyIcon').css("fill", "red")
                $('.emergencyIcon').addClass("transition-all duration-500 ease-in-out")
                navHeader.addClass('scrolled');
                $(".ihcIcon").attr("src", "{{ asset_gcs('public/assets/common/dcaed5ae-7c91-42d1-85ba-86ee245fc7fa.png') }}");
                $('.ihcIcon').addClass("transition-all duration-500 ease-in-out")
                $('.cartIcon').css("stroke", "#667085")
                $('.cartIcon').addClass("transition-all duration-500 ease-in-out")
                $('.avaHeader').css("fill", "#D9D9D9")
                $('.avaHeader').addClass("transition-all duration-500 ease-in-out")
                $('.barsIcon').css("fill", "#667085")
                $('.barsIcon').addClass("transition-all duration-500 ease-in-out")
                $('.profileBtn').removeClass('border-white');
                $('.profileBtn').addClass('border-neutral-40');
                $('.avatarBorder').removeClass('border-white');
                $('.avatarBorder').addClass('border-neutral-40');
                $('.button-header').attr('stroke', '#1D2939');
            } else {
                $('.header').removeClass('bg-white text-[#475467] shadow-md');
                $('.linguise_lang_item').removeClass('bg-white text-[#475467] shadow-md');
                $('.header').addClass('text-white').css('background-image', 'linear-gradient(180deg, #000000c7, #0000006e 80%, transparent)');
                $('#iconSearch').addClass("fill-white")
                $('#iconFill').removeClass("fill-[#EAFFCC]")
                $('.emergencyIcon').css("fill", "white")
                $('.emergencyIcon').addClass("transition-all duration-500 ease-in-out")
                navHeader.removeClass('scrolled');
                $(".ihcIcon").attr("src", "{{ asset_gcs('public/assets/common/7f72089d-8692-4df2-9142-16fcdfb6c2cd.png') }}");
                $('.ihcIcon').addClass("transition-all duration-500 ease-in-out")
                $('.cartIcon').css("stroke", "white")
                $('.cartIcon').addClass("transition-all duration-500 ease-in-out")
                $('.avaHeader').css("fill", "none")
                $('.avaHeader').addClass("transition-all duration-500 ease-in-out")
                $('.barsIcon').css("fill", "white")
                $('.barsIcon').addClass("transition-all duration-500 ease-in-out")
                $('.profileBtn').removeClass('border-neutral-40');
                $('.profileBtn').addClass('border-white');
                $('.avatarBorder').removeClass('border-neutral-40');
                $('.avatarBorder').addClass('border-white');
                $('.button-header').attr('stroke', 'white');
            }
        });

        $('#patientCareDropdownTrigger').click(function() {
            $('#patientCareDropdownContent').toggle();
        });

        $('#internationalPartnershipsCareDropdownTrigger').click(function() {
            $('#internationalPartnershipsCareDropdownContent').toggle();
        });
        $('#medicalTravelDropdownTrigger').click(function() {
            $('#medicalTravelDropdownContent').toggle();
        });

        $(document).click(function(event) {
            if (!$(event.target).closest('#patientCareDropdownTrigger').length &&
                !$(event.target).closest('#patientCareDropdownContent').length) {
                $('#patientCareDropdownContent').hide();
            }
            if (!$(event.target).closest('#internationalPartnershipsCareDropdownTrigger').length &&
                !$(event.target).closest('#internationalPartnershipsCareDropdownContent').length) {
                $('#internationalPartnershipsCareDropdownContent').hide();
            }
            if (!$(event.target).closest('#medicalTravelDropdownTrigger').length &&
                !$(event.target).closest('#medicalTravelDropdownContent').length) {
                $('#medicalTravelDropdownContent').hide();
            }
        });
    });
</script>
@endpush()
@endif

@push('script')
<script>
    function callModal() {

        const modal = document.getElementById("mobile-menu");

        if (modal.classList.contains("hidden")) {
            modal.classList.remove("hidden");
        } else {
            modal.classList.add("hidden");
        }
    }

    function onHandleOpenModalPackage() {

        // AJAX request
        fetch('{{ route("ajax.package_types") }}', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                var container = document.getElementById("dropdownContainer");
                container.innerHTML = '';
                data.data.forEach(function(item) {
                    // Create <a> element
                    var anchor = document.createElement("a");
                    anchor.setAttribute("href", "/medical-packages/type/" + item.slug);
                    anchor.classList.add("flex", "items-center", "gap-x-3.5", "py-2", "px-3", "rounded-lg", "text-sm", "text-gray-800", "text-gray-500","linguise_package_type");
                    anchor.textContent = item.name; // You can set the text content based on the data

                    // Append <a> element to the container div
                    container.appendChild(anchor);
                });
            })
            .catch(error => {
                // Handle errors
                console.error('Error:', error);
            });
    }

    function onHandleOpenModalPackageMobile() {

        // AJAX request
        fetch('{{ route("ajax.package_types") }}', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                var container = document.getElementById("dropdownContainerMobile");
                container.innerHTML = '';
                data.data.forEach(function(item) {
                    // Create <a> element
                    var anchor = document.createElement("a");
                    anchor.setAttribute("href", "/medical-packages/type/" + item.slug);
                    anchor.classList.add("flex", "items-center", "gap-x-3.5", "py-2", "px-3", "text-sm", "text-gray-800", "text-gray-500","linguise_package_type");
                    anchor.textContent = item.name; // You can set the text content based on the data

                    // Append <a> element to the container div
                    container.appendChild(anchor);
                });
            })
            .catch(error => {
                // Handle errors
                console.error('Error:', error);
            });
    }
</script>
@endpush
