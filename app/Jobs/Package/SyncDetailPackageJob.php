<?php

namespace App\Jobs\Package;

use App\Models\LandingPage\Package;
use App\Models\PackageDetailTest;
use App\Models\PackageFilter;
use App\Repositories\Simrs\Models\DetailPackage;
use App\Repositories\Simrs\SimrsRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SyncDetailPackageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected SimrsRepository $simrsRepository;

    /**
     * Create a new job instance.
     * $simrs_id -> from trakcare (package_code)
     */
    public function __construct(protected string $simrs_id)
    {
        $this->onQueue('request-trakcare-queue');
        $this->simrsRepository = new SimrsRepository();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        try {

            $data               = new DetailPackage();
            $data->package_id   = $this->simrs_id;

            $repo               = $this->simrsRepository->detailPackage($data);
            throw_if($repo->status() != 200);

            $simrsPackage           = $repo->data();
            $package                = Package::whereSimrsId($this->simrs_id)->first();
            $package->is_published  = true;
            $package->published_at  = now();

            $updateData             = [];
//            if(@$simrsPackage['restrictions']){
//
//
//                // Initialize variables
//                $minimumAge = null;
//                $maximumAge = null;
//                $gender = 0; // Default to 0 if both or none exist
//
//                // Track the presence of genders
//                $hasMale = false;
//                $hasFemale = false;
//
//                foreach ($simrsPackage['restrictions'] as $item) {
//                    // Determine minimum and maximum age
//                    if (isset($item['age_from'])) {
//                        $ageFrom = (int) $item['age_from'];
//                        $minimumAge = is_null($minimumAge) ? $ageFrom : min($minimumAge, $ageFrom);
//                    }
//
//                    if (isset($item['age_to'])) {
//                        $ageTo = (int) $item['age_to'];
//                        $maximumAge = is_null($maximumAge) ? $ageTo : max($maximumAge, $ageTo);
//                    }
//
//                    // Track gender presence
//                    if (isset($item['gender'])) {
//                        if ($item['gender'] === 'Male') {
//                            $hasMale = true;
//                        } elseif ($item['gender'] === 'Female') {
//                            $hasFemale = true;
//                        }
//                    }
//                }
//
//                // Determine the $gender value based on the presence of Male and/or Female
//                if ($hasMale && !$hasFemale) {
//                    $gender = 2; // Only Male exists
//                } elseif ($hasFemale && !$hasMale) {
//                    $gender = 1; // Only Female exists
//                } else {
//                    $gender = 0; // Both exist or neither exists
//                }
//
//                $package->min_age   = $minimumAge;
//                $package->max_age   = $maximumAge;
//                $package->gender    = $gender;
//
//            }

            if(@$simrsPackage['package_name']){
                $package->title = $simrsPackage['package_name'];
                $package->slug  = Str::slug($simrsPackage['package_name']);
            }

            if(@$simrsPackage['package_description']){
                $package->content = $simrsPackage['package_description'];
            }

            if(@$package['price']){
                $package->price = (float) $simrsPackage['price'];
            }

            if(@$simrsPackage['details']){
                foreach ($simrsPackage['details'] as $detail) {
                    if(!@$detail['section']){
                        continue;
                    }

                    $abbreviation = null;
                    if(@$detail['item_abbreviation']){
                        $abbreviation = $detail['item_abbreviation'];
                    }elseif(@$detail['orderset_description']){
                        $abbreviation = $detail['orderset_description'];
                    }

                    if (!$abbreviation) {
                        continue;
                    }


                    foreach (explode('|', $abbreviation) as $abbr) {
                        PackageDetailTest::updateOrCreate([
                            'package_id'    => $package->id,
                            'section'       => $detail['section'],
                            'abbrivation'   => $abbr,
                        ],[
                            'code'          => @$detail['item_code'],
                        ]);
                    }

                }
            }

            $package->save();

            if(@$simrsPackage['restrictions']){
                PackageFilter::wherePackageId($package->id)->delete();
                $data = [];
                foreach ($simrsPackage['restrictions'] as $restriction) {

                    $data[]             = [
                        'age_min'       => @$restriction['age_from'] ? (int)$restriction['age_from'] : 0,
                        'age_max'       => @$restriction['age_to'] ? (int)$restriction['age_to'] : 200,
                        'gender'        => $restriction['gender'],
                        'package_id'    => $package->id,
                        'created_at'    => now(),
                        'updated_at'    => now(),
                    ];
                }

                if($data){
                    PackageFilter::insert($data);
                }
            }

        }catch (\Throwable $th){

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            $this->fail($th->getMessage());;
        }
    }
}
