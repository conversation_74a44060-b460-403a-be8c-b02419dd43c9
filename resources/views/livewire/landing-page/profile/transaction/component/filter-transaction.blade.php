<div class="relative">

    <div class="flex flex-row justify-between items-center">

        <div class="flex flex-row gap-4 bg-blue-100 bg-opacity-10 p-2 rounded-2xl">
            @if($status == 1)
                <div class="bg-white rounded-xl px-3 pb-3 pt-3 flex flex-row gap-2 items-center text-[#16A34A] hover:cursor-pointer border-b-[3px] border-b-[#16A34A]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M10.625 10.625C10.625 10.9702 10.3452 11.25 10 11.25C9.65482 11.25 9.375 10.9702 9.375 10.625C9.375 10.2798 9.65482 10 10 10C10.3452 10 10.625 10.2798 10.625 10.625Z" fill="url(#paint0_linear_6627_37803)"/>
                        <path d="M6.25 13.125C6.59518 13.125 6.875 12.8452 6.875 12.5C6.875 12.1548 6.59518 11.875 6.25 11.875C5.90482 11.875 5.625 12.1548 5.625 12.5C5.625 12.8452 5.90482 13.125 6.25 13.125Z" fill="url(#paint1_linear_6627_37803)"/>
                        <path d="M6.875 14.375C6.875 14.7202 6.59518 15 6.25 15C5.90482 15 5.625 14.7202 5.625 14.375C5.625 14.0298 5.90482 13.75 6.25 13.75C6.59518 13.75 6.875 14.0298 6.875 14.375Z" fill="url(#paint2_linear_6627_37803)"/>
                        <path d="M8.125 13.125C8.47018 13.125 8.75 12.8452 8.75 12.5C8.75 12.1548 8.47018 11.875 8.125 11.875C7.77982 11.875 7.5 12.1548 7.5 12.5C7.5 12.8452 7.77982 13.125 8.125 13.125Z" fill="url(#paint3_linear_6627_37803)"/>
                        <path d="M8.75 14.375C8.75 14.7202 8.47018 15 8.125 15C7.77982 15 7.5 14.7202 7.5 14.375C7.5 14.0298 7.77982 13.75 8.125 13.75C8.47018 13.75 8.75 14.0298 8.75 14.375Z" fill="url(#paint4_linear_6627_37803)"/>
                        <path d="M10 13.125C10.3452 13.125 10.625 12.8452 10.625 12.5C10.625 12.1548 10.3452 11.875 10 11.875C9.65482 11.875 9.375 12.1548 9.375 12.5C9.375 12.8452 9.65482 13.125 10 13.125Z" fill="url(#paint5_linear_6627_37803)"/>
                        <path d="M10.625 14.375C10.625 14.7202 10.3452 15 10 15C9.65482 15 9.375 14.7202 9.375 14.375C9.375 14.0298 9.65482 13.75 10 13.75C10.3452 13.75 10.625 14.0298 10.625 14.375Z" fill="url(#paint6_linear_6627_37803)"/>
                        <path d="M11.875 13.125C12.2202 13.125 12.5 12.8452 12.5 12.5C12.5 12.1548 12.2202 11.875 11.875 11.875C11.5298 11.875 11.25 12.1548 11.25 12.5C11.25 12.8452 11.5298 13.125 11.875 13.125Z" fill="url(#paint7_linear_6627_37803)"/>
                        <path d="M12.5 14.375C12.5 14.7202 12.2202 15 11.875 15C11.5298 15 11.25 14.7202 11.25 14.375C11.25 14.0298 11.5298 13.75 11.875 13.75C12.2202 13.75 12.5 14.0298 12.5 14.375Z" fill="url(#paint8_linear_6627_37803)"/>
                        <path d="M13.75 13.125C14.0952 13.125 14.375 12.8452 14.375 12.5C14.375 12.1548 14.0952 11.875 13.75 11.875C13.4048 11.875 13.125 12.1548 13.125 12.5C13.125 12.8452 13.4048 13.125 13.75 13.125Z" fill="url(#paint9_linear_6627_37803)"/>
                        <path d="M12.5 10.625C12.5 10.9702 12.2202 11.25 11.875 11.25C11.5298 11.25 11.25 10.9702 11.25 10.625C11.25 10.2798 11.5298 10 11.875 10C12.2202 10 12.5 10.2798 12.5 10.625Z" fill="url(#paint10_linear_6627_37803)"/>
                        <path d="M13.75 11.25C14.0952 11.25 14.375 10.9702 14.375 10.625C14.375 10.2798 14.0952 10 13.75 10C13.4048 10 13.125 10.2798 13.125 10.625C13.125 10.9702 13.4048 11.25 13.75 11.25Z" fill="url(#paint11_linear_6627_37803)"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.625 1.875C5.97018 1.875 6.25 2.15482 6.25 2.5V3.75H13.75V2.5C13.75 2.15482 14.0298 1.875 14.375 1.875C14.7202 1.875 15 2.15482 15 2.5V3.75H15.625C17.0057 3.75 18.125 4.86929 18.125 6.25V15.625C18.125 17.0057 17.0057 18.125 15.625 18.125H4.375C2.99429 18.125 1.875 17.0057 1.875 15.625V6.25C1.875 4.86929 2.99429 3.75 4.375 3.75H5V2.5C5 2.15482 5.27982 1.875 5.625 1.875ZM16.875 9.375C16.875 8.68464 16.3154 8.125 15.625 8.125H4.375C3.68464 8.125 3.125 8.68464 3.125 9.375V15.625C3.125 16.3154 3.68464 16.875 4.375 16.875H15.625C16.3154 16.875 16.875 16.3154 16.875 15.625V9.375Z" fill="url(#paint12_linear_6627_37803)"/>
                        <defs>
                            <linearGradient id="paint0_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint1_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint2_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint3_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint4_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint5_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint6_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint7_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint8_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint9_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint10_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint11_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                            <linearGradient id="paint12_linear_6627_37803" x1="1.875" y1="1.875" x2="18.125" y2="18.7344" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#16A34A"/>
                                <stop offset="1" stop-color="#16A34A" stop-opacity="0.2"/>
                            </linearGradient>
                        </defs>
                    </svg>
                    <span class="text-[#16A34A] font-medium">Ongoing</span>
                </div>
                <div wire:click="onHandleChangeStatus(2)" class="bg-transparent rounded-xl px-3 pb-3 pt-3 flex flex-row gap-2 items-center text-[#667085] hover:cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M10 3.125C6.20304 3.125 3.125 6.20304 3.125 10C3.125 13.797 6.20304 16.875 10 16.875C13.797 16.875 16.875 13.797 16.875 10C16.875 6.20304 13.797 3.125 10 3.125ZM1.875 10C1.875 5.51269 5.51269 1.875 10 1.875C14.4873 1.875 18.125 5.51269 18.125 10C18.125 14.4873 14.4873 18.125 10 18.125C5.51269 18.125 1.875 14.4873 1.875 10ZM10 4.375C10.3452 4.375 10.625 4.65482 10.625 5V9.375H13.75C14.0952 9.375 14.375 9.65482 14.375 10C14.375 10.3452 14.0952 10.625 13.75 10.625H10C9.65482 10.625 9.375 10.3452 9.375 10V5C9.375 4.65482 9.65482 4.375 10 4.375Z" fill="#667085"/>
                    </svg>
                    <span class="text-[#667085] font-light">History</span>
                </div>
                <div wire:click="onHandleChangeStatus(3)" class="bg-transparent rounded-xl px-3 pb-3 pt-3 flex flex-row gap-2 items-center text-[#667085] hover:cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M10 3.125C6.20304 3.125 3.125 6.20304 3.125 10C3.125 13.797 6.20304 16.875 10 16.875C13.797 16.875 16.875 13.797 16.875 10C16.875 6.20304 13.797 3.125 10 3.125ZM1.875 10C1.875 5.51269 5.51269 1.875 10 1.875C14.4873 1.875 18.125 5.51269 18.125 10C18.125 14.4873 14.4873 18.125 10 18.125C5.51269 18.125 1.875 14.4873 1.875 10ZM10 4.375C10.3452 4.375 10.625 4.65482 10.625 5V9.375H13.75C14.0952 9.375 14.375 9.65482 14.375 10C14.375 10.3452 14.0952 10.625 13.75 10.625H10C9.65482 10.625 9.375 10.3452 9.375 10V5C9.375 4.65482 9.65482 4.375 10 4.375Z" fill="#667085"/>
                    </svg>
                    <span class="text-[#667085] font-light">Invoice History</span>
                </div>
            @elseif($status == 2)
                <div wire:click="onHandleChangeStatus(1)" class="bg-transparent rounded-xl px-3 pb-3 pt-3 flex flex-row gap-2 items-center text-[#667085] hover:cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.625 1.875C5.97018 1.875 6.25 2.15482 6.25 2.5V3.75H13.75V2.5C13.75 2.15482 14.0298 1.875 14.375 1.875C14.7202 1.875 15 2.15482 15 2.5V3.75H15.625C17.0057 3.75 18.125 4.86929 18.125 6.25V15.625C18.125 17.0057 17.0057 18.125 15.625 18.125H4.375C2.99429 18.125 1.875 17.0057 1.875 15.625V6.25C1.875 4.86929 2.99429 3.75 4.375 3.75H5V2.5C5 2.15482 5.27982 1.875 5.625 1.875ZM4.375 5C3.68464 5 3.125 5.55964 3.125 6.25V7.20946C3.49272 6.99674 3.91964 6.875 4.375 6.875H15.625C16.0804 6.875 16.5073 6.99674 16.875 7.20946V6.25C16.875 5.55964 16.3154 5 15.625 5H4.375ZM16.875 9.375C16.875 8.68464 16.3154 8.125 15.625 8.125H4.375C3.68464 8.125 3.125 8.68464 3.125 9.375V15.625C3.125 16.3154 3.68464 16.875 4.375 16.875H15.625C16.3154 16.875 16.875 16.3154 16.875 15.625V9.375ZM9.375 10.625C9.375 10.2798 9.65482 10 10 10H10.0062C10.3514 10 10.6312 10.2798 10.6312 10.625V10.6312C10.6312 10.9764 10.3514 11.2562 10.0062 11.2562H10C9.65482 11.2562 9.375 10.9764 9.375 10.6312V10.625ZM11.25 10.625C11.25 10.2798 11.5298 10 11.875 10H11.8812C12.2264 10 12.5062 10.2798 12.5062 10.625V10.6312C12.5062 10.9764 12.2264 11.2562 11.8812 11.2562H11.875C11.5298 11.2562 11.25 10.9764 11.25 10.6312V10.625ZM13.125 10.625C13.125 10.2798 13.4048 10 13.75 10H13.7562C14.1014 10 14.3812 10.2798 14.3812 10.625V10.6312C14.3812 10.9764 14.1014 11.2562 13.7562 11.2562H13.75C13.4048 11.2562 13.125 10.9764 13.125 10.6312V10.625ZM5.625 12.5C5.625 12.1548 5.90482 11.875 6.25 11.875H6.25625C6.60143 11.875 6.88125 12.1548 6.88125 12.5V12.5062C6.88125 12.8514 6.60143 13.1312 6.25625 13.1312H6.25C5.90482 13.1312 5.625 12.8514 5.625 12.5062V12.5ZM7.5 12.5C7.5 12.1548 7.77982 11.875 8.125 11.875H8.13125C8.47643 11.875 8.75625 12.1548 8.75625 12.5V12.5062C8.75625 12.8514 8.47643 13.1312 8.13125 13.1312H8.125C7.77982 13.1312 7.5 12.8514 7.5 12.5062V12.5ZM9.375 12.5C9.375 12.1548 9.65482 11.875 10 11.875H10.0062C10.3514 11.875 10.6312 12.1548 10.6312 12.5V12.5062C10.6312 12.8514 10.3514 13.1312 10.0062 13.1312H10C9.65482 13.1312 9.375 12.8514 9.375 12.5062V12.5ZM11.25 12.5C11.25 12.1548 11.5298 11.875 11.875 11.875H11.8812C12.2264 11.875 12.5062 12.1548 12.5062 12.5V12.5062C12.5062 12.8514 12.2264 13.1312 11.8812 13.1312H11.875C11.5298 13.1312 11.25 12.8514 11.25 12.5062V12.5ZM13.125 12.5C13.125 12.1548 13.4048 11.875 13.75 11.875H13.7562C14.1014 11.875 14.3812 12.1548 14.3812 12.5V12.5062C14.3812 12.8514 14.1014 13.1312 13.7562 13.1312H13.75C13.4048 13.1312 13.125 12.8514 13.125 12.5062V12.5ZM5.625 14.375C5.625 14.0298 5.90482 13.75 6.25 13.75H6.25625C6.60143 13.75 6.88125 14.0298 6.88125 14.375V14.3812C6.88125 14.7264 6.60143 15.0062 6.25625 15.0062H6.25C5.90482 15.0062 5.625 14.7264 5.625 14.3812V14.375ZM7.5 14.375C7.5 14.0298 7.77982 13.75 8.125 13.75H8.13125C8.47643 13.75 8.75625 14.0298 8.75625 14.375V14.3812C8.75625 14.7264 8.47643 15.0062 8.13125 15.0062H8.125C7.77982 15.0062 7.5 14.7264 7.5 14.3812V14.375ZM9.375 14.375C9.375 14.0298 9.65482 13.75 10 13.75H10.0062C10.3514 13.75 10.6312 14.0298 10.6312 14.375V14.3812C10.6312 14.7264 10.3514 15.0062 10.0062 15.0062H10C9.65482 15.0062 9.375 14.7264 9.375 14.3812V14.375ZM11.25 14.375C11.25 14.0298 11.5298 13.75 11.875 13.75H11.8812C12.2264 13.75 12.5062 14.0298 12.5062 14.375V14.3812C12.5062 14.7264 12.2264 15.0062 11.8812 15.0062H11.875C11.5298 15.0062 11.25 14.7264 11.25 14.3812V14.375Z" fill="#667085"/>
                    </svg>
                    <span class="text-[#667085] font-light">Ongoing</span>
                </div>
                <div class="bg-white rounded-xl px-3 pb-3 pt-3 flex flex-row gap-2 items-center text-[#16A34A] hover:cursor-pointer border-b-[3px] border-b-[#16A34A]">
                    <svg xmlns="http://www.w3.org/2000/svg" 
       fill="none" viewBox="0 0 24 24" 
       stroke-width="1.5" stroke="currentColor" 
       class="w-5 h-5">
    <path stroke-linecap="round" stroke-linejoin="round" 
          d="M19.5 14.25V6.75A2.25 2.25 0 0017.25 4.5H6.75A2.25 2.25 0 004.5 6.75v10.5A2.25 2.25 0 006.75 19.5h10.5a2.25 2.25 0 002.25-2.25V14.25z" />
    <path stroke-linecap="round" stroke-linejoin="round" 
          d="M8.25 9h7.5M8.25 12h4.5" />
  </svg>
                    <span class="text-[#16A34A] font-medium">History</span>
                </div>
                <div wire:click="onHandleChangeStatus(3)" class="bg-transparent rounded-xl px-3 pb-3 pt-3 flex flex-row gap-2 items-center text-[#667085] hover:cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M10 3.125C6.20304 3.125 3.125 6.20304 3.125 10C3.125 13.797 6.20304 16.875 10 16.875C13.797 16.875 16.875 13.797 16.875 10C16.875 6.20304 13.797 3.125 10 3.125ZM1.875 10C1.875 5.51269 5.51269 1.875 10 1.875C14.4873 1.875 18.125 5.51269 18.125 10C18.125 14.4873 14.4873 18.125 10 18.125C5.51269 18.125 1.875 14.4873 1.875 10ZM10 4.375C10.3452 4.375 10.625 4.65482 10.625 5V9.375H13.75C14.0952 9.375 14.375 9.65482 14.375 10C14.375 10.3452 14.0952 10.625 13.75 10.625H10C9.65482 10.625 9.375 10.3452 9.375 10V5C9.375 4.65482 9.65482 4.375 10 4.375Z" fill="#667085"/>
                    </svg>
                    <span class="text-[#667085] font-light">Invoice History</span>
                </div>
            @elseif($status == 3)
                <div wire:click="onHandleChangeStatus(1)" class="bg-transparent rounded-xl px-3 pb-3 pt-3 flex flex-row gap-2 items-center text-[#667085] hover:cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.625 1.875C5.97018 1.875 6.25 2.15482 6.25 2.5V3.75H13.75V2.5C13.75 2.15482 14.0298 1.875 14.375 1.875C14.7202 1.875 15 2.15482 15 2.5V3.75H15.625C17.0057 3.75 18.125 4.86929 18.125 6.25V15.625C18.125 17.0057 17.0057 18.125 15.625 18.125H4.375C2.99429 18.125 1.875 17.0057 1.875 15.625V6.25C1.875 4.86929 2.99429 3.75 4.375 3.75H5V2.5C5 2.15482 5.27982 1.875 5.625 1.875ZM4.375 5C3.68464 5 3.125 5.55964 3.125 6.25V7.20946C3.49272 6.99674 3.91964 6.875 4.375 6.875H15.625C16.0804 6.875 16.5073 6.99674 16.875 7.20946V6.25C16.875 5.55964 16.3154 5 15.625 5H4.375ZM16.875 9.375C16.875 8.68464 16.3154 8.125 15.625 8.125H4.375C3.68464 8.125 3.125 8.68464 3.125 9.375V15.625C3.125 16.3154 3.68464 16.875 4.375 16.875H15.625C16.3154 16.875 16.875 16.3154 16.875 15.625V9.375ZM9.375 10.625C9.375 10.2798 9.65482 10 10 10H10.0062C10.3514 10 10.6312 10.2798 10.6312 10.625V10.6312C10.6312 10.9764 10.3514 11.2562 10.0062 11.2562H10C9.65482 11.2562 9.375 10.9764 9.375 10.6312V10.625ZM11.25 10.625C11.25 10.2798 11.5298 10 11.875 10H11.8812C12.2264 10 12.5062 10.2798 12.5062 10.625V10.6312C12.5062 10.9764 12.2264 11.2562 11.8812 11.2562H11.875C11.5298 11.2562 11.25 10.9764 11.25 10.6312V10.625ZM13.125 10.625C13.125 10.2798 13.4048 10 13.75 10H13.7562C14.1014 10 14.3812 10.2798 14.3812 10.625V10.6312C14.3812 10.9764 14.1014 11.2562 13.7562 11.2562H13.75C13.4048 11.2562 13.125 10.9764 13.125 10.6312V10.625ZM5.625 12.5C5.625 12.1548 5.90482 11.875 6.25 11.875H6.25625C6.60143 11.875 6.88125 12.1548 6.88125 12.5V12.5062C6.88125 12.8514 6.60143 13.1312 6.25625 13.1312H6.25C5.90482 13.1312 5.625 12.8514 5.625 12.5062V12.5ZM7.5 12.5C7.5 12.1548 7.77982 11.875 8.125 11.875H8.13125C8.47643 11.875 8.75625 12.1548 8.75625 12.5V12.5062C8.75625 12.8514 8.47643 13.1312 8.13125 13.1312H8.125C7.77982 13.1312 7.5 12.8514 7.5 12.5062V12.5ZM9.375 12.5C9.375 12.1548 9.65482 11.875 10 11.875H10.0062C10.3514 11.875 10.6312 12.1548 10.6312 12.5V12.5062C10.6312 12.8514 10.3514 13.1312 10.0062 13.1312H10C9.65482 13.1312 9.375 12.8514 9.375 12.5062V12.5ZM11.25 12.5C11.25 12.1548 11.5298 11.875 11.875 11.875H11.8812C12.2264 11.875 12.5062 12.1548 12.5062 12.5V12.5062C12.5062 12.8514 12.2264 13.1312 11.8812 13.1312H11.875C11.5298 13.1312 11.25 12.8514 11.25 12.5062V12.5ZM13.125 12.5C13.125 12.1548 13.4048 11.875 13.75 11.875H13.7562C14.1014 11.875 14.3812 12.1548 14.3812 12.5V12.5062C14.3812 12.8514 14.1014 13.1312 13.7562 13.1312H13.75C13.4048 13.1312 13.125 12.8514 13.125 12.5062V12.5ZM5.625 14.375C5.625 14.0298 5.90482 13.75 6.25 13.75H6.25625C6.60143 13.75 6.88125 14.0298 6.88125 14.375V14.3812C6.88125 14.7264 6.60143 15.0062 6.25625 15.0062H6.25C5.90482 15.0062 5.625 14.7264 5.625 14.3812V14.375ZM7.5 14.375C7.5 14.0298 7.77982 13.75 8.125 13.75H8.13125C8.47643 13.75 8.75625 14.0298 8.75625 14.375V14.3812C8.75625 14.7264 8.47643 15.0062 8.13125 15.0062H8.125C7.77982 15.0062 7.5 14.7264 7.5 14.3812V14.375ZM9.375 14.375C9.375 14.0298 9.65482 13.75 10 13.75H10.0062C10.3514 13.75 10.6312 14.0298 10.6312 14.375V14.3812C10.6312 14.7264 10.3514 15.0062 10.0062 15.0062H10C9.65482 15.0062 9.375 14.7264 9.375 14.3812V14.375ZM11.25 14.375C11.25 14.0298 11.5298 13.75 11.875 13.75H11.8812C12.2264 13.75 12.5062 14.0298 12.5062 14.375V14.3812C12.5062 14.7264 12.2264 15.0062 11.8812 15.0062H11.875C11.5298 15.0062 11.25 14.7264 11.25 14.3812V14.375Z" fill="#667085"/>
                    </svg>
                    <span class="text-[#667085] font-light">Ongoing</span>
                </div>
                <div wire:click="onHandleChangeStatus(2)" class="bg-transparent rounded-xl px-3 pb-3 pt-3 flex flex-row gap-2 items-center text-[#667085] hover:cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M10 3.125C6.20304 3.125 3.125 6.20304 3.125 10C3.125 13.797 6.20304 16.875 10 16.875C13.797 16.875 16.875 13.797 16.875 10C16.875 6.20304 13.797 3.125 10 3.125ZM1.875 10C1.875 5.51269 5.51269 1.875 10 1.875C14.4873 1.875 18.125 5.51269 18.125 10C18.125 14.4873 14.4873 18.125 10 18.125C5.51269 18.125 1.875 14.4873 1.875 10ZM10 4.375C10.3452 4.375 10.625 4.65482 10.625 5V9.375H13.75C14.0952 9.375 14.375 9.65482 14.375 10C14.375 10.3452 14.0952 10.625 13.75 10.625H10C9.65482 10.625 9.375 10.3452 9.375 10V5C9.375 4.65482 9.65482 4.375 10 4.375Z" fill="#667085"/>
                    </svg>
                    <span class="text-[#667085] font-light">History</span>
                </div>
                <div class="bg-white rounded-xl px-3 pb-3 pt-3 flex flex-row gap-2 items-center text-[#16A34A] hover:cursor-pointer border-b-[3px] border-b-[#16A34A]">
                    <svg xmlns="http://www.w3.org/2000/svg" 
       fill="none" viewBox="0 0 24 24" 
       stroke-width="1.5" stroke="currentColor" 
       class="w-5 h-5">
    <path stroke-linecap="round" stroke-linejoin="round" 
          d="M19.5 14.25V6.75A2.25 2.25 0 0017.25 4.5H6.75A2.25 2.25 0 004.5 6.75v10.5A2.25 2.25 0 006.75 19.5h10.5a2.25 2.25 0 002.25-2.25V14.25z" />
    <path stroke-linecap="round" stroke-linejoin="round" 
          d="M8.25 9h7.5M8.25 12h4.5" />
  </svg>
                    <span class="text-[#16A34A] font-medium">Invoice History</span>
                </div>
            @endif
            <div wire:loading wire:target="onHandleChangeStatus" class="w-1/3">
                <div class="bg-white rounded-xl py-[18px] w-full flex flex-row justify-center items-center gap-2 hover:cursor-not-allowed">
                    @include('landing-page.component.spinner')
                    <span class="text-base font-semibold text-brand">Processing..</span>
                </div>
            </div>
        </div>

        <div wire:click="onHandleModalFilter" class="py-[14px] px-4 flex flex-row gap-2 border border-brand justify-center items-center rounded-xl h-fit hover:cursor-pointer">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path d="M15.625 10.625L16.875 10.625C17.2202 10.625 17.5 10.3452 17.5 9.99999C17.5 9.65482 17.2202 9.375 16.875 9.375L15.625 9.37501C15.2798 9.37502 15 9.65484 15 10C15 10.3452 15.2798 10.625 15.625 10.625Z" fill="#0D4D8B"/>
                <path d="M10 5C10 4.65482 10.2798 4.375 10.625 4.375L16.875 4.37501C17.2202 4.37501 17.5 4.65484 17.5 5.00001C17.5 5.34519 17.2202 5.62501 16.875 5.62501L10.625 5.625C10.2798 5.625 10 5.34518 10 5Z" fill="#0D4D8B"/>
                <path d="M10 15C10 14.6548 10.2798 14.375 10.625 14.375L16.875 14.375C17.2202 14.375 17.5 14.6548 17.5 15C17.5 15.3452 17.2202 15.625 16.875 15.625L10.625 15.625C10.2798 15.625 10 15.3452 10 15Z" fill="#0D4D8B"/>
                <path d="M3.12501 5.62501L4.37501 5.625C4.72018 5.625 5 5.34517 5 4.99999C5 4.65482 4.72017 4.375 4.37499 4.375L3.12499 4.37501C2.77982 4.37502 2.5 4.65484 2.5 5.00002C2.5 5.3452 2.77983 5.62502 3.12501 5.62501Z" fill="#0D4D8B"/>
                <path d="M4.37501 15.625L3.12501 15.625C2.77983 15.625 2.5 15.3452 2.5 15C2.5 14.6548 2.77982 14.375 3.12499 14.375L4.37499 14.375C4.72017 14.375 5 14.6548 5 15C5 15.3452 4.72018 15.625 4.37501 15.625Z" fill="#0D4D8B"/>
                <path d="M2.5 10C2.5 9.65482 2.77982 9.375 3.125 9.375H9.375C9.72018 9.375 10 9.65482 10 10C10 10.3452 9.72018 10.625 9.375 10.625H3.125C2.77982 10.625 2.5 10.3452 2.5 10Z" fill="#0D4D8B"/>
                <path d="M7.5 3.125C6.46447 3.125 5.625 3.96447 5.625 5C5.625 6.03553 6.46447 6.875 7.5 6.875C8.53553 6.875 9.375 6.03553 9.375 5C9.375 3.96447 8.53553 3.125 7.5 3.125Z" fill="#0D4D8B"/>
                <path d="M10.625 10C10.625 8.96447 11.4645 8.125 12.5 8.125C13.5355 8.125 14.375 8.96447 14.375 10C14.375 11.0355 13.5355 11.875 12.5 11.875C11.4645 11.875 10.625 11.0355 10.625 10Z" fill="#0D4D8B"/>
                <path d="M7.5 13.125C6.46447 13.125 5.625 13.9645 5.625 15C5.625 16.0355 6.46447 16.875 7.5 16.875C8.53553 16.875 9.375 16.0355 9.375 15C9.375 13.9645 8.53553 13.125 7.5 13.125Z" fill="#0D4D8B"/>
            </svg>
            <span class="text-brand font-light text-base hidden sm:block">Filter</span>
        </div>

    </div>
    @if($isModalFilter)
        <div class="flex flex-col gap-4 absolute bg-white w-full p-6 rounded-3xl shadow-lg">
            <span class="text-base font-semibold text-[#0D4D8B]">Filter</span>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

                <div class="relative flex-col">
                    <span class="text-base font-light text-[#475467]">Appointment for</span>
                    <div wire:click="onHandleModalPatient"
                         class="hover:cursor-pointer mt-2 text-base font-light w-full hs-dropdown-toggle py-3
                px-4 inline-flex justify-between items-center gap-x-2 rounded-lg border border-gray-200 bg-white
                text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none">
                        {{ @$selectedPatient['fullname'] ? $selectedPatient['fullname'] : 'Select' }}
                        <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                    </div>
                    @if($isModalPatient)
                        <div class="hover:cursor-pointer rounded-xl flex flex-col p-4 border shadow absolute w-full bg-white mt-2 z-10">
                            @foreach($patients as $patient)
                                <div wire:click="onHandleSelectedPatient('{{$patient->id}}','{{$patient->fullname}}')"
                                     class="flex flex-row items-center gap-3 py-[10px]" id="parent">
                                    @if(@$selectedPatient['id'] == $patient->id)
                                        <div class="bg-[#16A34A] p-[2px] rounded">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                                <path d="M10 3L4.5 8.5L2 6" stroke="white" stroke-width="1.6666" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </div>
                                    @else
                                        <div class="p-[2px] rounded h-4 w-4 border hover:border-[#16A34A]" id="child">
                                        </div>
                                    @endif
                                    <span class="text-base font-light">{{ $patient->fullname }}</span>

                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>

                <div class="relative flex-col">
                    <span class="text-base font-light text-[#475467]">Type</span>
                    <div wire:click="onHandleModalType"
                         class="hover:cursor-pointer mt-2 text-base font-light w-full hs-dropdown-toggle py-3
                px-4 inline-flex justify-between items-center gap-x-2 rounded-lg border border-gray-200 bg-white
                text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none">
                        {{ @$selectedType['label'] ? $selectedType['label'] : 'Select'}}
                        <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                    </div>
                    @if($isModalType)
                        <div class="hover:cursor-pointer rounded-xl flex flex-col p-4 border shadow absolute w-full bg-white mt-2 z-10">
                            @foreach($types as $type)
                                <div wire:click="onHandleSelectedType('{{$type['key']}}','{{$type['label']}}')"
                                     class="flex flex-row items-center gap-3 py-[10px]" id="parent">
                                    @if(@$selectedType['key'] == $type['key'])
                                        <div class="bg-[#16A34A] p-[2px] rounded">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                                <path d="M10 3L4.5 8.5L2 6" stroke="white" stroke-width="1.6666" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </div>
                                    @else
                                        <div class="p-[2px] rounded h-4 w-4 border hover:border-[#16A34A]" id="child">
                                        </div>
                                    @endif
                                    <span class="text-base font-light">{{ $type['label'] }}</span>

                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>

                <div class="relative flex-col hidden">
                    <span class="text-base font-light text-[#475467]">Appointment Date</span>
                    <div wire:click="onHandleModalLanguage"
                         class="hover:cursor-pointer mt-2 text-base font-light w-full hs-dropdown-toggle py-3
                px-4 inline-flex justify-between items-center gap-x-2 rounded-lg border border-gray-200 bg-white
                text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none">
                        Select
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.25 2C6.66421 2 7 2.33579 7 2.75V4H14V2.75C14 2.33579 14.3358 2 14.75 2C15.1642 2 15.5 2.33579 15.5 2.75V4H15.75C17.2688 4 18.5 5.23122 18.5 6.75V15.25C18.5 16.7688 17.2688 18 15.75 18H5.25C3.73122 18 2.5 16.7688 2.5 15.25V6.75C2.5 5.23122 3.73122 4 5.25 4H5.5V2.75C5.5 2.33579 5.83579 2 6.25 2ZM5.25 7.5C4.55964 7.5 4 8.05964 4 8.75V15.25C4 15.9404 4.55964 16.5 5.25 16.5H15.75C16.4404 16.5 17 15.9404 17 15.25V8.75C17 8.05964 16.4404 7.5 15.75 7.5H5.25Z" fill="#667085"/>
                        </svg>
                    </div>
                    @if(false)
                        <div class="hover:cursor-pointer rounded-xl flex flex-col p-4 border shadow absolute w-full bg-white mt-2 z-10">
                            @foreach($languages as $language)
                                <div wire:click="onHandleSelectedLanguage('{{$language}}')"
                                     class="flex flex-row items-center gap-3 py-[10px]" id="parent">
                                    @if($selectedLanguage == $language)
                                        <div class="bg-[#16A34A] p-[2px] rounded">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                                <path d="M10 3L4.5 8.5L2 6" stroke="white" stroke-width="1.6666" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </div>
                                    @else
                                        <div class="p-[2px] rounded h-4 w-4 border hover:border-[#16A34A]" id="child">
                                        </div>
                                    @endif
                                    <span class="text-base font-light">{{ $language }}</span>

                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>

            </div>
            <div class="flex flex-row gap-4 mt-5">
                <div wire:click="onHandleReset"
                     class="border border-[#0D4D8B] py-3 w-full flex justify-center items-center rounded-xl hover:cursor-pointer">
                    <span class="text-[#0D4D8B]">Reset</span>
                </div>
                <div wire:click="onHandleSearch"
                     class="bg-[#0D4D8B] py-3 w-full flex justify-center items-center rounded-xl hover:cursor-pointer">
                    <span class="text-white">Search</span>
                </div>
            </div>
        </div>
    @endif
    @if($selectedType || $selectedPatient)
        <div class="flex flex-row gap-2 items-center mt-6">
            <span wire:click="onHandleRemoveFilter(1)" class="text-xs md:text-sm font-medium text-[#667085] pr-4 hover:cursor-pointer">Delete All</span>
            @if($selectedPatient)
                <div class="rounded-full flex items-center justify-center border border-[#16A34A] bg-[#E5FFEF] gap-2 py-2 px-4">
                    <span class="text-[#16A34A] text-xs md:text-sm font-medium">{{ $selectedPatient['fullname'] }}</span>
                    <div wire:click="onHandleRemoveFilter(2)" class="hover:cursor-pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                            <path d="M10.5 3.5L3.5 10.5" stroke="#16A34A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M3.5 3.5L10.5 10.5" stroke="#16A34A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
            @endif
            @if($selectedType)
                <div class="rounded-full flex items-center justify-center border border-[#16A34A] bg-[#E5FFEF] gap-2 py-2 px-4">
                    <span class="text-[#16A34A] text-xs md:text-sm font-medium">{{ $selectedType['label'] }}</span>
                    <div wire:click="onHandleRemoveFilter(3)" class="hover:cursor-pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                            <path d="M10.5 3.5L3.5 10.5" stroke="#16A34A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M3.5 3.5L10.5 10.5" stroke="#16A34A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
            @endif
        </div>
    @endif


</div>

