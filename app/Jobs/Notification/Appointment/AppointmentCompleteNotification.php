<?php

namespace App\Jobs\Notification\Appointment;

use App\Enums\General\GeneralInt;
use App\Enums\General\GeneralString;
use App\Repositories\LandingPage\AppointmentPatientSummary\AppointmentPatientSummaryRepository;
use App\Services\Notification\Email\AppointmentCompletedMailService;
use App\Services\Notification\FirebaseCloudMessage\AppointmentCompletedNotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class AppointmentCompleteNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected AppointmentPatientSummaryRepository $appointmentPatientSummaryRepository;

    /**
     * Create a new job instance.
     */
    public function __construct(protected string $uuid)
    {
        $this->onQueue('booking-notification');
        $this->appointmentPatientSummaryRepository = new AppointmentPatientSummaryRepository();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $appointmentPatientSummary = $this->appointmentPatientSummaryRepository->findByCondition([
            'uuid' => $this->uuid
        ]);

        // send to email
        $data = [
            'email'                 => $appointmentPatientSummary->patient->email,
            'doctor_title'          => '-', // TODO : DO THIS LATER
            'doctor_name'           => $appointmentPatientSummary->doctor->name,
            'patient_name'          => $appointmentPatientSummary->patient->fullname,
            'scheduled_date'        => $appointmentPatientSummary->book_date,
            'scheduled_time'        => $appointmentPatientSummary->book_time_from . ' - ' . $appointmentPatientSummary->book_time_to,
            'view_appointment_link' => route('profile.mybook.show', ['uuid' => $appointmentPatientSummary->uuid, 'type' => 1]),
            'view_medical_res_link' => route('profile.mybook.show', ['uuid' => $appointmentPatientSummary->uuid, 'type' => 1]), // TODO : CHANGE THIS LATER
            'support_contact_info'  => '089680988232' // TODO : CHANGE THIS LATER
        ];
        $this->sendToEmail($data);

        // send to firebase
        if(isset($appointmentPatientSummary->publicUser) && $appointmentPatientSummary->publicUser->login_device_type !== 0){
            $data = [
                'user_fcm_token'    => @$appointmentPatientSummary->publicUser->mobile_fcm_token,
                'doctor_title'      => '-', // TODO : DO THIS LATER
                'doctor_name'       => $appointmentPatientSummary->doctor->name,
                'scheduled_date'    => $appointmentPatientSummary->book_date,
                'scheduled_time'    => $appointmentPatientSummary->book_time_from . ' ' . $appointmentPatientSummary->book_time_to,
                'login_device_type' => $appointmentPatientSummary->publicUser->login_device_type,
                'custom_data'       => [
                    'data' => json_encode([
                        'patientId'     => $appointmentPatientSummary->patient->id,
                        'uuid'          => $appointmentPatientSummary->uuid,
                        'detailUuid'    => [
                            $appointmentPatientSummary->uuid
                        ],
                        'appointmentId' => $appointmentPatientSummary->simrs_registration_no,
                        'route'         => GeneralString::ROUTE_DETAIL_FIREBASE,
                        'type'          => GeneralInt::FIREBASE_APPOINTMENT_TYPE
                    ])
                ]
            ];
            $this->sendToFirebase($data);
        }


    }

    private function sendToFirebase($data): void
    {
        $service = (new AppointmentCompletedNotificationService($data))->call();

        $body = [
            "content"       => '[FIREBASE] CALLED  AppointmentCompletedNotificationService  WITH DATA ' . json_encode($data,true) . ' RESPONSE ' . json_encode($service),
            "avatar_url"    =>
                "https://media.licdn.com/dms/image/D4E0BAQEtbeSJkNL14Q/company-logo_200_200/0/1664787920525/bithealth_logo?e=**********&v=beta&t=WdlP1wknXx5_nLF7A9CkWDAH9rQn6-aGHrlIZK-XjqI",
            "username"      => "developer-bithealth"
        ];

        $this->sendToDiscord($body);

    }

    private function sendToEmail($data): void
    {
        $service = (new AppointmentCompletedMailService($data))->call();

        $body = [
            "content"       => '[EMAIL] CALLED  AppointmentCompletedMailService  WITH DATA ' . json_encode($data,true) . ' RESPONSE ' . json_encode($service),
            "avatar_url"    =>
                "https://media.licdn.com/dms/image/D4E0BAQEtbeSJkNL14Q/company-logo_200_200/0/1664787920525/bithealth_logo?e=**********&v=beta&t=WdlP1wknXx5_nLF7A9CkWDAH9rQn6-aGHrlIZK-XjqI",
            "username"      => "developer-bithealth"
        ];

        $this->sendToDiscord($body);

    }
    private function sendToDiscord($body): void
    {
        Http::withHeaders([
            'Content-Type' => 'application/json'
        ])->post(config('discord.notification_channel.general_notification'), $body);
    }

    public function tags()
    {
        return ['UUID ' . $this->uuid];
    }
}
