<?php

namespace App\Livewire\Landingpage\Doctor;

use App\Models\Doctor;
use Livewire\Component;
use App\Models\BihConfigs;
use App\Enums\Table\BihConfig\Key;
use Illuminate\Support\Facades\DB;
use App\Enums\Table\BihConfig\Group;
use App\Services\Simrs\Doctor\Schedule\WeeklyService;

class ShowDoctor extends Component
{

    public $uuid;
    public $doctorCSWALink;
    public $schedules;

    public function mount($uuid)
    {
        $this->uuid = $uuid;
    }

    public function render()
    {
        $BooleanLinkAppoitmentExternal = false;
        $LinkAppoitmentExternal = null;
        $doctor = Doctor::whereuuid($this->uuid)->with([
            'specialty',
            'certificates',
            'clinicalInterests',
            'fellowships',
            'regularSchedules',
            'languages',
            'medicalSchools'
        ])->first();

        if (!$doctor) {
            $this->redirectRoute('landing-page.homepage');
        }
        $this->doctorCSWALink = @bih_config_value(Key::CUSTOMER_SERVICE, Group::CONTACT_INFORMATION);
        $scheduleService = (new WeeklyService([
            'doctor_id' => $doctor->simrs_doctor_id
        ], true))->call();
        if ($scheduleService->status() == 200) {
            $this->schedules = $scheduleService->data();
        }

        $doctors_clinic_icon = DB::table('doctors_clinic_icon')
            ->where('simrs_doctor_id', $doctor->simrs_doctor_id)
            ->first();

        if ($doctors_clinic_icon) {
            $BooleanLinkAppoitmentExternal = true;
            $LinkAppoitmentExternal = $doctors_clinic_icon->redirect_url;
        } 

        // if ($doctor->simrs_doctor_id == "**********") {
        //     $BooleanLinkAppoitmentExternal = true;
        //     $LinkAppoitmentExternal = 'https://iconcancercentre.id/en/make-an-appointment/?utm_source=bih&utm_medium=website&utm_content=bih_drwibisono_appointmentpage';
        // } else if ($doctor->simrs_doctor_id == "3300000021") {
        //     $BooleanLinkAppoitmentExternal = true;
        //     $LinkAppoitmentExternal = 'https://iconcancercentre.id/en/make-an-appointment/?utm_source=bih&utm_medium=website&utm_content=bih_drganapati_appointmentpage';
        // } elseif ($doctor->simrs_doctor_id == "3300000680") {
        //     //Dr. Tan Yew Oo, MBBS, FRCP
        //     $BooleanLinkAppoitmentExternal = true;
        //     $LinkAppoitmentExternal = 'https://iconcancercentre.id/en/make-an-appointment/?utm_source=bih&utm_medium=website&utm_content=bih_drganapati_appointmentpage';
        // } elseif ($doctor->simrs_doctor_id == "3300000677") {
        //     //Dr. Francis Chin Kuok Choon
        //     $BooleanLinkAppoitmentExternal = true;
        //     $LinkAppoitmentExternal = 'https://iconcancercentre.id/en/make-an-appointment/?utm_source=bih&utm_medium=website&utm_content=bih_drganapati_appointmentpage';
        // } elseif ($doctor->simrs_doctor_id == "3300000678") {
        //     //Dr. Patricia Kho Sunn Sunn
        //     $BooleanLinkAppoitmentExternal = true;
        //     $LinkAppoitmentExternal = 'https://iconcancercentre.id/en/make-an-appointment/?utm_source=bih&utm_medium=website&utm_content=bih_drganapati_appointmentpage';
        // } elseif ($doctor->simrs_doctor_id == "3300000679") {
        //     //Dr. Robert Lim
        //     $BooleanLinkAppoitmentExternal = true;
        //     $LinkAppoitmentExternal = 'https://iconcancercentre.id/en/make-an-appointment/?utm_source=bih&utm_medium=website&utm_content=bih_drganapati_appointmentpage';
        // }

        return view('livewire.landingpage.doctor.show-doctor', [
            'doctor' => $doctor,
            'BooleanLinkAppoitmentExternal' => $BooleanLinkAppoitmentExternal,
            'LinkAppoitmentExternal' => $LinkAppoitmentExternal
        ]);
    }
}
