@extends('layouts.admin')

@section('title', 'Page 2')

@section('css')
    <link rel="stylesheet" href="{{ asset('/cms-assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('/cms-assets/vendor/libs/bootstrap-select/bootstrap-select.css') }}" />
@endsection

@section('content')

    <div class="container-xxl flex-grow-1 container-p-y">
        <h4 class="py-3 mb-4"><span class="text-muted fw-light">Doctor Profile /</span> Edit</h4>
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">

                        <div class="mb-3">

                            <form action="{{ route('cms.doctor-profiles.update', $doctor->id) }}" method="POST"
                                class="needs-validation" enctype="multipart/form-data" novalidate>
                                @csrf
                                @method('PUT')

                                <div class="mb-3">
                                    <label for="image" class="form-label">Foto Profile</label> <span
                                        class="text-danger">*</span> &nbsp; &nbsp; &nbsp;

                                    <div class="input-group">
                                        <input type="file" id="image" name="image" accept='image/*'
                                            class="form-control" @if ($doctor->image == null || $doctor->image == '') required @endif />
                                        <a href="{{ $doctor->image }}" class="btn btn-outline-primary"
                                            id="inputGroupFileAddon04" target="_blank">View Image</a>
                                    </div>

                                    <span class="text-small text-muted">Max file size 2 MB. Recommended resolution 512x512
                                        (ratio 1:1).</span>
                                    <div class="valid-feedback"></div>
                                    <div class="invalid-feedback">Please enter image content.</div>

                                </div>

                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name</label> <span
                                        class="text-danger">*</span>
                                    <input type="text" class="form-control" id="name" name="name"
                                        value="{{ $doctor->name }}" disabled>
                                    <div class="valid-feedback"></div>
                                    <div class="invalid-feedback">Please enter your email.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="term_conditions" class="form-label">Description</label>
                                    <textarea class="form-control" id="profile_description" name="profile_description" rows="6">{{ $doctor->profile_description }}</textarea>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <label for="type" class="form-label">Gender</label>
                                        <span class="text-danger">*</span>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-check form-check-inline mt-3">
                                            <input class="form-check-input" type="radio" name="gender" id="radio-type"
                                                value="F" required @if ($doctor->gender == 'F') checked @endif>
                                            <label class="form-check-label" for="type">Female</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="gender" id="radio-type"
                                                value="M" required
                                                @if ($doctor->gender == 'M') checked @endif>
                                            <label class="form-check label" for="type">Male</label>
                                        </div>
                                    </div>
                                    <div class="col-md-12 ">
                                        <div class="valid-feedback"></div>
                                        <div class="invalid-feedback">Please select a gender.</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="name" class="form-label">Speciality</label> <span
                                        class="text-danger">*</span>
                                    <input type="text" class="form-control" id="speciality" name="speciality"
                                        value="{{ $doctor->specialty->group_name_en }}" disabled>
                                    <div class="valid-feedback"></div>
                                    <div class="invalid-feedback">Please enter your email.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="experience_year" class="form-label">Year of Experience</label>
                                    <div class="input-group">
                                        <input type="number" id="experience_year" name="experience_year"
                                            class="form-control" value="{{ $doctor->experience_year }}" />
                                        <span href="#" class="btn btn-outline-primary" id="">Year</span>
                                    </div>
                                    <div class="valid-feedback"></div>
                                    <div class="invalid-feedback">Please enter Year of Experience.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="languages" class="form-label">Languages</label> <span
                                        class="text-danger">*</span>
                                    <select id="languages" class="select2 form-select" data-style="btn-default"
                                        data-live-search="true" name="languages[]" multiple>
                                        @foreach ($languages as $language)
                                            <option value="{{ $language['id'] }}" @@
                                                @if (in_array($language['id'], $doctor->languages->pluck('name')->toArray())) selected="selected" @endif>
                                                {{ $language['name'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="valid-feedback"></div>
                                    <div class="invalid-feedback">Please select a language.</div>
                                </div>

                                <div class="mb-3">
                                    <h3 for="education">Medical Education
                                    </h3>
                                    <div class="form-repeater-education">
                                        <div data-repeater-list="educations">
                                            <div data>
                                            </div>
                                            <div data-repeater-item>
                                                <div class="row">
                                                    <div class="mb-3 col-md-8 mb-0">
                                                        <label class="form-label" for="form-repeater">Name</label>
                                                        <span class="text-danger">*</span>
                                                        <input type="text" id="education-name" class="form-control"
                                                            required name="education-name" placeholder="Name" />
                                                    </div>
                                                    <div class="mb-3 col-md-2 mb-0">
                                                        <label class="form-label" for="form-repeater">Year</label>
                                                        
                                                        <input type="text" id="education-year"
                                                            oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                                                            class="form-control" name="education-year"
                                                            placeholder="Year" />
                                                    </div>

                                                    <div class="mb-3 col-md-2 d-flex align-items-center mb-0">
                                                        <span class="btn btn-label-danger mt-4" data-repeater-delete>
                                                            <i class="ti ti-trash ti-xs me-1"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-0">
                                            <button class="btn btn-primary" data-repeater-create>
                                                <i class="ti ti-plus me-1"></i>
                                                <span class="align-middle">Add</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <h3 for="sertification">Board Certification
                                    </h3>
                                    <div class="form-repeater-certification">
                                        <div data-repeater-list="certifications">
                                            <div data-repeater-item>
                                                <div class="row">
                                                    <div class="mb-3 col-md-8 mb-0">
                                                        <label class="form-label" for="form-repeater">Title</label>
                                                        <span class="text-danger">*</span>
                                                        <input type="text" id="certification-name"
                                                            class="form-control" required name="certification-name"
                                                            placeholder="Name" />
                                                    </div>
                                                    <div class="mb-3 col-md-2 mb-0">
                                                        <label class="form-label" for="form-repeater">Year</label>
                                                        
                                                        <input type="text" id="certification-year"
                                                            oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                                                            class="form-control" name="certification-year"
                                                            placeholder="Year" />
                                                    </div>

                                                    <div class="mb-3 col-md-2 d-flex align-items-center mb-0">
                                                        <span class="btn btn-label-danger mt-4" data-repeater-delete>
                                                            <i class="ti ti-trash ti-xs me-1"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-0">
                                            <span class="btn btn-primary" data-repeater-create>
                                                <i class="ti ti-plus me-1"></i>
                                                <span class="align-middle">Add</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <h3 for="fellowship">Fellowship
                                    </h3>
                                    <div class="form-repeater-fellowship">
                                        <div data-repeater-list="fellowships">
                                            <div data-repeater-item>
                                                <div class="row">
                                                    <div class="mb-3 col-md-8 mb-0">
                                                        <label class="form-label" for="form-repeater">Name</label>
                                                        <span class="text-danger">*</span>
                                                        <input type="text" id="fellowship-name" class="form-control"
                                                            required name="fellowship-name" placeholder="Name" />
                                                    </div>
                                                    <div class="mb-3 col-md-2 mb-0">
                                                        <label class="form-label" for="form-repeater">Year</label>
                                                        
                                                        <input type="text" id="fellowship-year"
                                                            oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                                                            class="form-control" name="fellowship-year"
                                                            placeholder="Year" />
                                                    </div>

                                                    <div class="mb-3 col-md-2 d-flex align-items-center mb-0">
                                                        <span class="btn btn-label-danger mt-4" data-repeater-delete>
                                                            <i class="ti ti-trash ti-xs me-1"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-0">
                                            <button class="btn btn-primary" data-repeater-create>
                                                <i class="ti ti-plus me-1"></i>
                                                <span class="align-middle">Add</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <h3 for="interest">Clinical Interest
                                    </h3>
                                    <div class="form-repeater-interest">
                                        <div data-repeater-list="interests">
                                            <div data-repeater-item>
                                                <div class="row">
                                                    <div class="mb-3 col-md-10 mb-0">
                                                        <label class="form-label" for="form-repeater">Name</label>
                                                        <span class="text-danger">*</span>
                                                        <input type="text" id="interest-name" class="form-control"
                                                            required name="interest-name" placeholder="Name" />
                                                    </div>

                                                    <div class="mb-3 col-md-2 d-flex align-items-center mb-0">
                                                        <span class="btn btn-label-danger mt-4" data-repeater-delete>
                                                            <i class="ti ti-trash ti-xs me-1"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-0">
                                            <button class="btn btn-primary" data-repeater-create>
                                                <i class="ti ti-plus me-1"></i>
                                                <span class="align-middle">Add</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="float-end">
                                    <a href="{{ route('cms.doctor-profiles') }}" class="btn btn-link">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Submit</button>
                                </div>
                            </form>

                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

@endsection


@section('js')

    <script src="{{ asset('/cms-assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('/cms-assets/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>

    <script src="{{ asset('/cms-assets/vendor/libs/jquery-repeater/jquery-repeater.js') }}"></script>

    <script>
        $(function() {
            $('.select2').select2();
            var $formRepeaterEdication = $('.form-repeater-education').repeater({
                initEmpty: true,
                show: function() {
                    $(this).slideDown();
                },
                hide: function(e) {
                    confirm('Are you sure you want to delete this element?') && $(this).slideUp(e);
                }
            });

            @if ($doctor->medicalSchools->count() > 0)
                $formRepeaterEdication.setList([
                    @foreach ($doctor->medicalSchools as $education)
                        {
                            'education-name': '{{ $education->name }}',
                            'education-year': '{{ $education->year }}'
                        },
                    @endforeach
                ]);
            @endif

            var $formRepeaterCertification = $('.form-repeater-certification').repeater({
                initEmpty: true,
                show: function() {
                    $(this).slideDown();
                },
                hide: function(e) {
                    confirm('Are you sure you want to delete this element?') && $(this).slideUp(e);
                }
            });

            @if ($doctor->certificates->count() > 0)
                $formRepeaterCertification.setList([
                    @foreach ($doctor->certificates as $certification)
                        {
                            'certification-name': '{{ $certification->name }}',
                            'certification-year': '{{ $certification->year }}'
                        },
                    @endforeach
                ]);
            @endif



            var $formRepeaterFellowship = $('.form-repeater-fellowship').repeater({
                initEmpty: true,
                show: function() {
                    $(this).slideDown();
                },
                hide: function(e) {
                    confirm('Are you sure you want to delete this element?') && $(this).slideUp(e);
                }
            });

            @if ($doctor->fellowships->count() > 0)
                $formRepeaterFellowship.setList([
                    @foreach ($doctor->fellowships as $fellowship)
                        {
                            'fellowship-name': '{{ $fellowship->name }}',
                            'fellowship-year': '{{ $fellowship->year }}'
                        },
                    @endforeach
                ]);
            @endif

            var $formRepeaterInterest = $('.form-repeater-interest').repeater({
                initEmpty: true,
                show: function() {
                    $(this).slideDown();
                },
                hide: function(e) {
                    confirm('Are you sure you want to delete this element?') && $(this).slideUp(e);
                }
            });

            @if ($doctor->clinicalInterests->count() > 0)
                $formRepeaterInterest.setList([
                    @foreach ($doctor->clinicalInterests as $interest)
                        {
                            'interest-name': '{{ $interest->name }}'
                        },
                    @endforeach
                ]);
            @endif

        });
    </script>
@endsection
