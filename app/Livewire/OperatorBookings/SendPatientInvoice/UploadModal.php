<?php

namespace App\Livewire\OperatorBookings\SendPatientInvoice;

use App\Jobs\Notification\MedicalPackage\Email\MedicalPackageReportMailNotification;
use Livewire\Component;
use Livewire\WithFileUploads;
use App\Services\GCS\GoogleCloudService;
// use App\Jobs\Notification\MedicalPackage\Email\MedicalPackageReportMailNotification;
use App\Repositories\Patient\PatientRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;


class UploadModal extends Component
{
    use WithFileUploads;
    protected $listeners = [
        'medical-package-updated-form-data' => 'onListenUpdatedData' // Method name to call
    ];
    public $file;
    public $patient;

    public $wireKey;
    public $selectedPatient;

    protected $rules = [
        'file' => 'required|file', // 10MB max
        'patient' => 'required',
    ];

    protected $messages = [
        'file.required' => 'Please select a file',
        'patient.required' => 'Please select a patient',
    ];

    protected $disk;
    
    public function __construct()
    {
        $this->disk = Storage::disk('local');
    }

    public function mount()
    {
        $this->wireKey = 'upload-modal-' . \Illuminate\Support\Str::uuid();
    }

    public function updatedFile()
    {
        $fileSizeBytes = $this->file->getSize();
        $fileSizeMB = round($fileSizeBytes / (1024 * 1024), 2);
        $this->skipRender();
    }

    public function save()
    {
        try {
            $this->dispatch('show-spinner');
            
            $this->validate();

            $patient = (new PatientRepository())->getById($this->patient['patient_id']);
            if (!$patient) {
                throw new \Exception('Patient not found');
            }

            // $formatted = Carbon::createFromFormat('Y-m-d', $patient->dob)->format('dmY');

            $path = $this->file->store('uploads', ['disk' => 'local']);
            $fullPath = storage_path('app/' . $path);

            // $newPath = $this->encryptPdf(
            //     $fullPath,
            //     $formatted
            // );

            $newFile = new UploadedFile(
                $fullPath,
                basename($fullPath),
                null,
                true
            );

            $fileName   = pathinfo($this->file->getClientOriginalName(), PATHINFO_FILENAME);
            $path       = (new GoogleCloudService())->uploadFileNoCompress($newFile, '/invoice', $fileName);
            $url = (new GoogleCloudService())->getStaticUrl($path);

            \App\Models\PatientInvoiceSendLogs::create([
                'patient_id' => $this->patient['patient_id'],
                'filename' => $this->file->getClientOriginalName(),
                'created_by' => auth()->user()->id,
                'url' => $url,
            ]);

            @unlink($fullPath);
            // @unlink($newPath);

            // MedicalPackageReportMailNotification::dispatch($this->patient['patient_id'], $url);
            
            return redirect()->route('cms.bookings.send-patient-invoice')->with('success', 'File uploaded successfully!');
        }catch (\Throwable $th) {
            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);
            $this->dispatch('hide-spinner');
            throw $th;
        }
    }

    public function render()
    {
        return view('livewire.operator-bookings.send-patient-invoice.upload-modal',
            [
                'wireKey' => $this->wireKey,
            ]);
    }

    public function onListenUpdatedData($data, $wireKey)
    {

        if(@$data['selected_patient_id']){
            $this->selectedPatient['patient_id'] = $data['selected_patient_id'];
        }

        if(@$data['selected_patient_simrs_id']){
            $this->selectedPatient['patient_simrs_id'] = $data['selected_patient_simrs_id'];
        }

        if(@$data['selected_patient_public_user_id']){
            $this->selectedPatient['public_user_id'] = $data['selected_patient_public_user_id'];
        }

        if(@$data['selected_patient_email']){
            $this->selectedPatient['email'] = $data['selected_patient_email'];
        }

        $this->patient = $this->selectedPatient;
        $this->skipRender();

    }

    public function encryptPdf($inputPath, $password)
    {
           // Cek apakah pdftk bisa dipanggil (lintas OS)
    $isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';
    $checkCommand = $isWindows ? 'where pdftk' : 'which pdftk';
    
    $pdftkPath = shell_exec($checkCommand);

    if (empty($pdftkPath)) {
        throw new \Exception('pdftk is not installed or not in PATH');
    }

    $tempOutput = storage_path('app/temp_encrypted.pdf');

    // Escape path (Windows khususnya rawan spasi)
    $inputPathEscaped = escapeshellarg($inputPath);
    $tempOutputEscaped = escapeshellarg($tempOutput);
    $passwordEscaped = escapeshellarg($password);

    $command = "pdftk {$inputPathEscaped} output {$tempOutputEscaped} user_pw {$passwordEscaped}";
    Log::info("Encrypting PDF with command: {$command}");

    shell_exec($command);

    if (!file_exists($tempOutput)) {
        throw new \Exception('PDF encryption failed');
    }

    return $tempOutput;
    }
}
