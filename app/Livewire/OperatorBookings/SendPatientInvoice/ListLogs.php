<?php

namespace App\Livewire\OperatorBookings\SendPatientInvoice;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\PatientInvoiceSendLogs;


class ListLogs extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 10;
    public $sortBy = 'created_at'; // Default sort column
    public $sortDirection = 'desc'; // Default sort direction

    // Update query when sorting is applied
    public function sort($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function render()
    {
        $logs = PatientInvoiceSendLogs::query()
            ->orderBy($this->sortBy, $this->sortDirection) // Apply sorting
            ->paginate($this->perPage);
            
        return view('livewire.operator-bookings.send-patient-invoice.list-logs', [
            'logs' => $logs
        ]);
    }
}
