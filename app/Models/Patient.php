<?php

namespace App\Models;

use App\Enums\Table\BihConfig\Group;
use App\Enums\Table\PatientAddress\AddressType;
use App\Models\LandingPage\PublicUser;
use App\Observers\PatientObserver;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

#[ObservedBy([PatientObserver::class])]
class Patient extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    protected $appends = ['is_complete_data'];

    public function getGenderLabelAttribute(): string
    {
        switch ($this->attributes['gender']) {
            case 1:
                return "Laki-laki";
            case 2:
                return "Perempuan";
            default:
                return "Lainnya";
        }
    }

    public function getReligionLabelAttribute(): string
    {
        return match ($this->attributes['religion']) {
            1 => "Islam",
            2 => "Kristen",
            3 => "Katholik",
            4 => "Hindu",
            5 => "Budha",
            6 => "Konghucu",
            default => "Lainnya",
        };
    }

    public function getMaritalLabelAttribute(): string
    {
        return match ($this->attributes['marital_status']) {
            1 => "Belum Menikah",
            2 => "Menikah",
            3 => "Cerai Hidup",
            4 => "Cerai Mati",
            default => "Lainnya",
        };
    }

    public function getJobLabelAttribute(): string
    {
        return match ($this->attributes['job']) {
            1 => "Tidak Bekerja",
            2 => "PNS",
            3 => "TNI/POLRI",
            4 => "BUMN",
            5 => "Pegawai Swasta/Wirausaha",
            6 => "Lainnya",
            default => $this->attributes['job'],
        };
    }

    public function getIsMandatoryCompleteAttribute(): bool
    {
        return
            !empty($this->attributes['first_name']) &&
            !empty($this->attributes['last_name']) &&
            !empty($this->attributes['dob']) &&
            !empty($this->attributes['gender']) &&
            !empty($this->attributes['email']) &&
            !empty($this->attributes['contact_no']);
    }

    public function getIsCompleteDataAttribute(): bool
    {
        if (!$this->is_mandatory_complete) {
            return false;
        }

        $optionalFields = [
            'mother_name',
            'place_of_birth',
            'religion',
            'language',
            'is_complete_address',
            'last_education',
            'job',
            'marital_status',
            'caregiver_name',
            'caregiver_contact',
            'caregiver_relation',
            'covid_vaccine'
        ];

        foreach ($optionalFields as $field) {
            if (empty($this->attributes[$field])) {
                return false;
            }
        }

        return true;
    }

    // public function getIsCompleteDataAttribute(): bool
    // {
    //     $status = true;
    //     if (!@$this->attributes['first_name']) $status = false;
    //     if (!@$this->attributes['first_name'] || !$this->attributes['last_name']) $status = false;
    //     if (!@$this->attributes['mother_name']) $status = false;
    //     if (!@$this->attributes['place_of_birth']) $status = false;
    //     if (!@$this->attributes['dob']) $status = false;
    //     if (!@$this->attributes['gender']) $status = false;
    //     if (!@$this->attributes['religion']) $status = false;
    //     if (!@$this->attributes['language']) $status = false;
    //     if (!@$this->attributes['gender']) $status = false;
    //     if (!@$this->is_complete_address) $status = false;
    //     if (!@$this->attributes['contact_no']) $status = false;
    //     if (!@$this->attributes['email']) $status = false;
    //     if (!@$this->attributes['last_education']) $status = false;
    //     if (!@$this->attributes['job']) $status = false;
    //     if (!@$this->attributes['marital_status']) $status = false;
    //     if (!@$this->attributes['caregiver_name']) $status = false;
    //     if (!@$this->attributes['caregiver_contact']) $status = false;
    //     if (!@$this->attributes['caregiver_relation']) $status = false;
    //     if (!@$this->attributes['covid_vaccine']) $status = false;

    //     return $status;
    // }

    public function getIsCompleteAddressAttribute(): bool
    {
        $isComplete = true;
        foreach ($this->patientAddresses as $patientAddress) {

            if (@$this->attributes['passport_number'] && @!$this->attributes['ktp_number']) {

                if ($patientAddress->address_type == 1) {

                    if (!$patientAddress->address || !$patientAddress->country) {
                        $isComplete = false;
                        break;
                    };
                }
            } else {

                if ($patientAddress->address_type == 1) {

                    if (
                        !$patientAddress->address
                        || !$patientAddress->country
                        || !$patientAddress->province
                        || !$patientAddress->city
                        || !$patientAddress->subdistrict
                        || !$patientAddress->ward
                        || !$patientAddress->rt
                        || !$patientAddress->rw
                        || !$patientAddress->postal_code
                    ) {
                        $isComplete = false;
                        break;
                    };
                }
            }


            if ($patientAddress->address_type == 2) {

                if (
                    !$patientAddress->address
                    || !$patientAddress->country
                    || !$patientAddress->province
                    || !$patientAddress->city
                    || !$patientAddress->subdistrict
                    || !$patientAddress->ward
                    || !$patientAddress->rt
                    || !$patientAddress->rw
                    || !$patientAddress->postal_code
                ) {
                    $isComplete = false;
                    break;
                };
            }
        }

        return $isComplete;
    }

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uuid = (string) Str::uuid();
        });
    }

    public function phoneCountryCodes(): HasMany
    {
        return $this->hasMany(CountryCode::class, 'id', 'contact_country_code_id');
    }

    public function whatsappCountryCodes(): HasMany
    {
        return $this->hasMany(CountryCode::class, 'id', 'whatsapp_country_code_id');
    }

    public function contactCountry(): HasOne
    {
        return $this->hasOne(CountryCode::class, 'id', 'contact_country_code_id');
    }

    public function caregiverContactCountryCodes(): HasMany
    {
        return $this->hasMany(CountryCode::class, 'id', 'caregiver_contact_country_code_id');
    }

    public function getRelationLabelAttribute(): string
    {
        // TODO : CONVERT THIS TO BIHCONFIGS
        return match ($this->attributes['relationship']) {
            1 => "Myself",
            2 => "Parent",
            3 => "Child",
            4 => "Spouse",
            5 => "Relative/Sibling",
            6 => "Other",
            default => "Lainnya",
        };
    }

    public function getContactWithCodeAttribute()
    {
        if (@$this->phoneCountryCodes->first()->extension) {
            return '+' . $this->phoneCountryCodes->first()->extension . $this->attributes['contact_no'];
        } else {
            return $this->attributes['contact_no'];
        }
    }

    public function patientAddresses(): HasMany
    {
        return $this->hasMany(PatientAddress::class);
    }
    
    public function patientInvoiceSendLogs(): HasMany
    {
        return $this->hasMany(PatientInvoiceSendLogs::class);
    }

    public function identityCardAddress(): HasMany
    {
        return $this->hasMany(PatientAddress::class, 'id', 'address_place_id');
    }

    public function domicileAddress(): HasMany
    {
        return $this->hasMany(PatientAddress::class, 'id', 'domicile_place_id');
    }

    public function getAgeAttribute(): int
    {
        return Carbon::parse($this->attributes['dob'])->age ?? 0;
    }

    public function getethnicityLabelAttribute(): string
    {
        return @BihConfigs::where([
            'group' => Group::ETHNIC,
            'value'   => $this->ethnicity
        ])->first()->key ?? '-';
    }

    public function idCardAddress()
    {
        return $this->hasOne(PatientAddress::class)->where('address_type', AddressType::ID_CARD);
    }

    public function residenceAddress()
    {
        return $this->hasOne(PatientAddress::class)->where('address_type', AddressType::RESIDENCE);
    }

    public function getFullNameAttribute(): string
    {
        if ($this->attributes['is_only_first_name']) {
            return $this->attributes['first_name'];
        }
        return $this->attributes['first_name'] . ' ' . $this->attributes['last_name'];
    }

    public function getTitleSimrsAttribute(): string
    {
        return match ($this->attributes['gender']) {
            'F' => 'Ms',
            default => 'Mr',
        };
    }

    public function contactHomeCountryCode(): BelongsTo
    {
        return $this->belongsTo(CountryCode::class, 'contact_home_country_code_id');
    }
    public function getContactHomeWithCodeAttribute()
    {
        if (@$this->contactHomeCountryCode) {
            return '+' . $this->contactHomeCountryCode->extension . $this->attributes['contact_home'];
        } else {
            return $this->attributes['contact_home'];
        }
    }

    public function contactCaregiverCountryCode(): BelongsTo
    {
        return $this->belongsTo(CountryCode::class, 'caregiver_contact_country_code_id');
    }

    public function getContactCaregiverWithCodeAttribute()
    {
        if (@$this->contactCaregiverCountryCode) {
            return '+' . $this->contactCaregiverCountryCode->extension . $this->attributes['caregiver_contact'];
        } else {
            return $this->attributes['caregiver_contact'];
        }
    }

    public function getPublicUserAttribute()
    {
        if ($this->attributes['public_user_id']) {
            return PublicUser::find($this->attributes['public_user_id']);
        } else {
            return Patient::where('id', $this->attributes['parent_id'])->first()->publicUser;
        }
    }
}
