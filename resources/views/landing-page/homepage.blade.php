@section('title', 'Bali International Hospital | Sanur, Bali')
@section('seo_meta')
    <meta name="description" content="Experience world-class healthcare at Bali International Hospital. Cutting-edge treatments, compassionate care—book your appointment today!">
    <link rel="canonical" href="https://bih.id">
    <link rel="alternate" hreflang="en-id" href="https://bih.id/">
@endsection

@extends('landing-page.layouts.masterTemplate')

<link rel="preload" href="https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.snow.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.snow.css"></noscript>
<style>
    body {
        margin: 0;
    }

    .movingColors {
        /*width: 100vw;*/
        /*height: 100vh;*/
        background: white;
    }

    .movingColors p {
        width: 400px;
        height: 400px;
        border-radius: 400px;
        backface-visibility: hidden;
        position: absolute;
        animation-name: move;
        animation-duration: 6s;
        animation-timing-function: linear;
        animation-iteration-count: infinite;
    }

    .movingColors p:nth-child(1) {
        color: #c9e1ff;
        top: 200%;
        left: 0;
        animation-duration: 13.7s;
        animation-delay: -14s;
        transform-origin: -16vw 2vh;
        box-shadow: -800px 0 214.0438363536px currentColor;
    }

    .movingColors p:nth-child(2) {
        color: #e7ffd5;
        top: 200%;
        left: 0;
        animation-duration: 13s;
        animation-delay: -14.3s;
        transform-origin: 25vw -7vh;
        box-shadow: 800px 0 135.6046803315px currentColor;
    }

    .movingColors p:nth-child(3) {
        color: #e7ffd5;
        top: 200%;
        left: 0;
        animation-duration: 14.1s;
        animation-delay: -10.2s;
        transform-origin: -16vw -17vh;
        box-shadow: -800px 0 183.0183599821px currentColor;
    }

    .movingColors p:nth-child(4) {
        color: #fffadf;
        top: 200%;
        left: 0;
        animation-duration: 12.6s;
        animation-delay: -10.9s;
        transform-origin: -20vw -8vh;
        box-shadow: 800px 0 245.4538106578px currentColor;
    }

    @keyframes move {
        100% {
            transform: translate3d(0, 0, 1px) rotate(360deg);
        }
    }
</style>

@section('content')

    <div class="flex flex-col mx-4 xl:mx-40 gap-20">
        <div class="xl:-mx-40 -mx-4 relative z-10">
            @include('landing-page.partials.homepage.banner')
        </div>
        <div class="z-10">
            @include('landing-page.partials.homepage.redefining')
        </div>
        <div class="z-10">
            @include('landing-page.partials.homepage.klinik-partner')
        </div>
        <div class="-mx-4 xl:-mx-40 -mt-20 z-10">
            @include('landing-page.partials.homepage.center-of-excelent-banner')
        </div>
        <div class="z-10">
            @include('landing-page.partials.homepage.state-technology')
        </div>
        <div class="movingColors hidden lg:block">
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
        </div>

        <div class="movingColors2 hidden lg:block">
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
        </div>
        <div id="medical-package-section" class="lg:-mt-40">
            @include('landing-page.partials.homepage.packages',['packageTypes'=>$package_types])
        </div>

        @if($promos)
            @if ($promos->count() ?? [] > 0)
                @include('landing-page.partials.homepage.promos', [
                    'promos' => $promos,
                ])
            @endif
        @endif

        <div class="z-10">
            @include('landing-page.partials.homepage.gallery')
        </div>

        @php
            $highlight      = $articles['highlight'] ?? null;
            $highlightArray = $highlight ? $highlight->toArray() : [];
        @endphp
        @if (count(!empty($articles['articles']) ? $highlightArray : []) > 0 ||
                (!empty($articles['articles']) && $articles['articles']->count() > 0))
            <div class="z-10">
                @include('landing-page.partials.homepage.blog-news', [
                    'articles' => $articles,
                ])
            </div>
        @endif

        <div class="z-10">
            @include('landing-page.partials.homepage.partnership')
        </div>

    </div>
    @if (!empty($announcement))
        @include('landing-page.partials.homepage.announcement')
    @endif
    <div class="z-10 -mb-10 mt-6">
        @include('landing-page.partials.homepage.contact')
    </div>
@endsection

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-element-bundle.min.js"></script>
@endpush
