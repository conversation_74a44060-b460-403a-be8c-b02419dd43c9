<?php

namespace App\Jobs\Notification\MedicalPackage\Email;

use App\Repositories\LandingPage\PackageSummaryDetail\PackageSummaryDetailRepository;
use App\Services\Notification\Email\MedicalPackageCompletedMailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class CompleteNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected PackageSummaryDetailRepository $packageSummaryDetailRepository;

    /**
     * Create a new job instance.
     * @param string $uuid // $uuid = package_summary_details.uuid
     */
    public function __construct(protected string $uuid)
    {
        $this->onQueue('booking-notification');
        $this->packageSummaryDetailRepository = new PackageSummaryDetailRepository();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $packageSummaryDetail = $this->packageSummaryDetailRepository->findByCondition([
            'uuid' => $this->uuid
        ]);

        $data = [
            'email'                     => $packageSummaryDetail->packageSummary->publicUser->email,
            'medical_package_name'      => $packageSummaryDetail->package_title,
            'patient_name'              => $packageSummaryDetail->patient->fullname,
            'scheduled_date'            => $packageSummaryDetail->visit_date,
            'scheduled_time'            => 'null',
            'view_medical_res_link'     => route('profile.mybook.show', ['uuid' => $packageSummaryDetail->packageSummary->uuid, 'type' => 3]), // TODO : CHANGE THIS LATER
            'support_contact_info'      => '089680988232' // TODO : CHANGE THIS LATER
        ];

        $service = (new MedicalPackageCompletedMailService($data))->call();

        $body = [
            "content"       => '[EMAIL - MEDICAL PACKAGE][MedicalPackageCompletedMailService WITH REQUEST BODY : '. json_encode($data).' : RESPONSE SERVICE ]' . json_encode($service),
            "avatar_url"    =>
                "https://media.licdn.com/dms/image/D4E0BAQEtbeSJkNL14Q/company-logo_200_200/0/1664787920525/bithealth_logo?e=**********&v=beta&t=WdlP1wknXx5_nLF7A9CkWDAH9rQn6-aGHrlIZK-XjqI",
            "username"      => "developer-bithealth"
        ];

        Http::withHeaders([
            'Content-Type' => 'application/json'
        ])->post(config('discord.notification_channel.general_notification'), $body);

    }
}
