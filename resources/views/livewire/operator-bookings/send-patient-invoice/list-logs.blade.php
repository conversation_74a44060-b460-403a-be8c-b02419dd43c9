<div class="">

    <!-- Table -->
    <div class="overflow-x-auto bg-white rounded-lg shadow">
        <table class="min-w-full divide-y divide-gray-200">
             <thead class="bg-gray-50">
                <tr>
                    <!-- Sortable Headers -->
                    <th 
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    >
                        Patient Name
                    </th>
                    <th 
                        wire:click="sort('created_at')" 
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    >
                        Sent At
                        @if ($sortBy === 'created_at')
                            @if ($sortDirection === 'asc')
                                &#9650; <!-- Up arrow -->
                            @else
                                &#9660; <!-- Down arrow -->
                            @endif
                        @endif
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach ($logs as $log)
                    <tr wire:key="log-{{ $log->id }}">
                        <td class="px-6 py-4 whitespace-nowrap">{{ $log->patient->fullname }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ $log->created_at->format('d/m/Y H:i:s') }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="mt-4">
        {{ $logs->links('livewire::tailwind') }}
    </div>
</div>