<?php

namespace App\Jobs\Package;

use App\Models\LandingPage\Package;
use App\Models\LandingPage\PackageCategory;
use App\Repositories\Simrs\Models\ListPackage;
use App\Repositories\Simrs\SimrsRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SyncPackageByCategoryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected SimrsRepository $simrsRepository;

    /**
     * Create a new job instance.
     * $category_id -> from trakcare (billing_subgroup_code)
     */
    public function __construct(protected string $category_id)
    {
        $this->onQueue('request-trakcare-queue');
        $this->simrsRepository = new SimrsRepository();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {

            $packageCategory    = PackageCategory::where('simrs_id', $this->category_id)->first();

            $data               = new ListPackage();
            $data->category_id  = $this->category_id;

            $repo = $this->simrsRepository->listPackageByCategoryId($data);

            if($repo->status() != 200){
                info('ERROR '  . json_encode($repo) . ' THE DATA ' . json_encode($data));
                throw new \Exception($repo->data()['error']);
            }

            $packages = $repo->data()['data'];

            foreach ($packages as $package){
                if(!@$package['package_code'] || !@$package['package_name']){
                    continue;
                }

                Package::updateOrCreate([
                    'simrs_id'              => $package['package_code'],
                    'package_category_id'   => $packageCategory->id,
                    'package_type_id'       => $packageCategory->package_type_id,
                ],[
                    'title'                 => $package['package_name'],
                    'slug'                  => Str::slug($package['package_name']),
                    'is_published'          => false,
                    'content'               => $package['package_description'],
                    'price'                 => (float)@$package['price'],
                ]);

                SyncDetailPackageJob::dispatch($package['package_code']);
            }

        }catch (\Throwable $th){

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);
            $this->fail($th->getMessage());
        }
    }
}
