<div class="">
    <!-- Search Input -->
    <div class="mb-4">
        <input
            type="text"
            wire:model.live.="search"
            placeholder="Search users..."
            class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
    </div>

    <!-- Table -->
    <div class="overflow-x-auto bg-white rounded-lg shadow">
        <table class="min-w-full divide-y divide-gray-200">
             <thead class="bg-gray-50">
                <tr>
                    <!-- Sortable Headers -->
                    <th 
                        wire:click="sort('fullname')" 
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    >
                        Name
                        @if ($sortBy === 'fullname')
                            @if ($sortDirection === 'asc')
                                &#9650; <!-- Up arrow -->
                            @else
                                &#9660; <!-- Down arrow -->
                            @endif
                        @endif
                    </th>
                    <th 
                        wire:click="sort('email')" 
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    >
                        Email
                        @if ($sortBy === 'email')
                            @if ($sortDirection === 'asc')
                                &#9650; <!-- Up arrow -->
                            @else
                                &#9660; <!-- Down arrow -->
                            @endif
                        @endif
                    </th>
                    <th 
                        wire:click="sort('created_at')" 
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    >
                        Created At
                        @if ($sortBy === 'created_at')
                            @if ($sortDirection === 'asc')
                                &#9650; <!-- Up arrow -->
                            @else
                                &#9660; <!-- Down arrow -->
                            @endif
                        @endif
                    </th>
                    <th>
                        Action
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach ($patients as $patient)
                    <tr wire:key="patient-{{ $patient->id }}">
                        <td class="px-6 py-4 whitespace-nowrap">{{ $patient->fullname }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ $patient->email }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ $patient->created_at->format('Y-m-d') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none" aria-label="Close" data-hs-overlay="#hs-large-modal-{{$patient->uuid}}">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class="hover:cursor-pointer">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M20.6705 3.3295C20.2312 2.89017 19.5188 2.89017 19.0795 3.3295L17.9223 4.48667L19.5133 6.07766L20.6705 4.9205C21.1098 4.48116 21.1098 3.76884 20.6705 3.3295ZM18.4527 7.13832L16.8617 5.54733L8.46085 13.9482C8.02029 14.3887 7.69644 14.9321 7.51857 15.5292L7.11458 16.8854L8.47078 16.4814C9.0679 16.3036 9.61129 15.9797 10.0519 15.5391L18.4527 7.13832ZM18.0188 2.26884C19.044 1.24372 20.706 1.24372 21.7312 2.26884C22.7563 3.29397 22.7563 4.95603 21.7312 5.98116L11.1125 16.5998C10.4957 17.2166 9.73498 17.67 8.899 17.919L6.21411 18.7188C5.95019 18.7974 5.6644 18.7251 5.46967 18.5303C5.27494 18.3356 5.20259 18.0498 5.28121 17.7859L6.08099 15.101C6.33001 14.265 6.7834 13.5043 7.40019 12.8875L18.0188 2.26884ZM5.25 6.74999C4.42157 6.74999 3.75 7.42156 3.75 8.24999V18.75C3.75 19.5784 4.42157 20.25 5.25 20.25H15.75C16.5784 20.25 17.25 19.5784 17.25 18.75V14C17.25 13.5858 17.5858 13.25 18 13.25C18.4142 13.25 18.75 13.5858 18.75 14V18.75C18.75 20.4068 17.4069 21.75 15.75 21.75H5.25C3.59315 21.75 2.25 20.4068 2.25 18.75V8.24999C2.25 6.59314 3.59315 5.24999 5.25 5.24999H10C10.4142 5.24999 10.75 5.58578 10.75 5.99999C10.75 6.4142 10.4142 6.74999 10 6.74999H5.25Z" fill="#0D4D8B" />
                                </svg>
                            </button>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    @foreach ($patients as $patient)

    <div id="hs-large-modal-{{$patient->uuid}}" class="hs-overlay hidden relative z-10" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="fixed inset-0 bg-gray-500/75 transition-opacity" aria-hidden="true"></div>

        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            
            <div class="bg-white w-2/3 p-6 rounded-lg">
            <div class="flex justify-between items-start py-3">
                <h3 id="hs-large-modal-label" class="font-bold text-gray-800">
                    Add New Patient
                </h3>
                <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none" aria-label="Close" data-hs-overlay="#hs-large-modal-{{$patient->uuid}}">
                        <span class="sr-only">Close</span>
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6 6 18"></path>
                            <path d="m6 6 12 12"></path>
                        </svg>
                    </button>
            </div>
                <livewire:operator-bookings.patients.new-patient page="/cms/bookings/patients" :patient_id="$patient->id" wire:key="patient-{{\Illuminate\Support\Str::uuid()}}"/>
            </div>
        </div>
    </div>
    @endforeach

    <!-- Pagination -->
    <div class="mt-4">
        {{ $patients->links('livewire::tailwind') }}
    </div>
</div>

@livewireScripts